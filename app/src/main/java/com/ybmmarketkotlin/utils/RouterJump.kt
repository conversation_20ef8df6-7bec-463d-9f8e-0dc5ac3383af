package com.ybmmarketkotlin.utils

import android.content.Intent
import com.ybmmarket20.activity.InvoiceListActivity
import com.ybmmarket20.activity.InvoiceListPopActivity
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.ROUTER_SEARCH_PRODUCT
import com.ybmmarket20.constant.ROUTER_SEARCH_PRODUCT_OP
import com.ybmmarket20.search.SearchProductOPActivity
import com.ybmmarket20.utils.RoutersUtils
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object RouterJump {


    /**
     * 跳转搜索页
     */
    fun jump2SearchPage(){
        val url = "$ROUTER_SEARCH_PRODUCT?pageSource=${IntentCanst.PAGE_SOURCE_HOME_SEARCH}"
        RoutersUtils.open(url)
    }

    /**
     * 跳转搜索页
     */
    fun jump2SearchOPPage(mEntrance:String = ""){
        var url = "$ROUTER_SEARCH_PRODUCT_OP?pageSource=${IntentCanst.PAGE_SOURCE_HOME_SEARCH}"
        if (mEntrance.isNotEmpty()){
            url += "&${SearchProductOPActivity.INTENT_ENTRANCE}=$mEntrance"
        }
        RoutersUtils.open(url)
    }

    /**
     * 跳转购物车
     */
    fun jump2ShopCar(){
        RoutersUtils.open("ybmpage://main/2")
    }

    /**
     * 跳转商品详情页
     * @param mUrl String
     * @param params Map<String, Any>
     */
    fun jump2ProductDetail(mUrl:String="",params: Map<String,Any> = mapOf()){

        try {
            val url = mUrl.ifEmpty { "ybmpage://productdetail?" }
            val jumpUrl = splicingUrlWithParams(url,params)

            RoutersUtils.open(jumpUrl)
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

    /**
     * 跳转到发票页 查看发票
     */
    fun jump2InvoiceList(orderDetailBean: CheckOrderDetailBean) {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val id: Int = orderDetailBean.id
        val orderNo: String = orderDetailBean.orderNo
        val createTime: String = dateFormat.format(Date(orderDetailBean.createTime))
        val payTime: String = dateFormat.format(Date(orderDetailBean.payTime))
        //参数过长，使用 intent
        val intent: Intent
        if (orderDetailBean.isThirdCompany()) {
            // POP 售后发票页面
            val aptitudeStatus = if (orderDetailBean.invoiceAsStateName == "申请发票售后") "1" else "0"
            intent = Intent(YBMAppLike.getApp().currActivity, InvoiceListPopActivity::class.java).apply {
                putExtra("orderid", id.toString())
                putExtra("number", orderNo)
                putExtra("time", createTime)
                putExtra("paytime", payTime)
                putExtra("billInfo", orderDetailBean.billInfo)

                putExtra("aptitudeStatus", aptitudeStatus)
                putExtra("orgId", orderDetailBean.orgId.toString())
                putExtra("orgName", orderDetailBean.companyName)
                putExtra("afterSalesNo", orderDetailBean.invoiceAfterSalesNo)
                putExtra("afterSaleDetailHtmlUrl", orderDetailBean.afterSaleDetailHtmlUrl)
                putExtra("applyInvoiceHtmlUrl", orderDetailBean.applyInvoiceHtmlUrl)
            }

        } else {
            // 自营发票页面
            intent = Intent(YBMAppLike.getApp().currActivity, InvoiceListActivity::class.java).apply {
                putExtra("orderid", id.toString())
                putExtra("number", orderNo)
                putExtra("time", createTime)
                putExtra("paytime", payTime)
                putExtra("billInfo", orderDetailBean.billInfo)
            }
        }

        YBMAppLike.getApp().currActivity.startActivity(intent)
    }

    /**
     * 跳转到售后详情 查看发票售后进度
     */
    fun jump2InvoiceDetail(mAfterSalesNo: String) {
        RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo=$mAfterSalesNo")
    }
}