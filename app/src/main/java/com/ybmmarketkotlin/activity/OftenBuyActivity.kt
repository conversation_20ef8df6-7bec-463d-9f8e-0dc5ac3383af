package com.ybmmarketkotlin.activity

import android.os.Bundle
import com.analysys.ANSAutoPageTracker
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.home.OftenBuyFragment
import com.ybmmarket20.utils.analysis.XyyIoUtil

@Router("oftenBuy")
class OftenBuyActivity : BaseActivity(), ANSAutoPageTracker{
    override fun getContentViewId() = R.layout.activity_often_buy

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        XyyIoUtil.track("frequently_purchased_list_click")
        fragment = OftenBuyFragment()
        supportFragmentManager.beginTransaction().apply {
            add(R.id.fl_container, fragment, "oftenBuy")
            commitNow()
            show(fragment)
        }
    }



    private lateinit var fragment: OftenBuyFragment

    override fun initData() {
        setTitle("常购清单")
    }

    override fun registerPageProperties(): MutableMap<String, Any> {
        val map = HashMap<String, Any>()
        map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackOftenBuy.PAGE_ID
        map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackOftenBuy.TITLE
        return map
    }

    override fun registerPageUrl(): String = this.getFullClassName()

}