package com.ybmmarket<PERSON>lin.views

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.util.AttributeSet
import android.util.SparseArray
import android.util.SparseIntArray
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.utils.UiUtils

class FlexBoxLayoutMaxLines : LinearLayout {

    var mTagViewClickListener: ((Int) -> Unit)? = null
    var mEntrance:String = ""

    companion object {
        val FROM_SEARCH: String = "search"
    }

    fun addTagView(tagView: TextView) {

        cacheViewArray.put(cacheViewArray.size(), tagView)

        if (canAddTag) {
            calcAndAddTag(tagView, tagView.layoutParams as LayoutParams)
        } else {
            calcAndAddMoreView()
        }
    }

    fun clear() {
        cacheViewArray.clear()
        totalSize = 0
        canAddTag = true
        expandView = null
        removeAllViews()
    }

    private fun calcAndAddMoreView() {

        if (expandView != null) {
            return
        }

        val lastlinearLayout = getChildAt(maxLines - 1) as LinearLayout
        expandView = View.inflate(context, R.layout.item_search_expand, null)
        expandView?.setOnClickListener {
            canAddTag = true
            isExpand = true
            removeMoreView()
            addCachedUnAddView()
        }

        val lp = LayoutParams(lineHeight, lineHeight)
        lp.leftMargin = UiUtils.dp2px(10)
        lp.rightMargin = UiUtils.dp2px(10)

        val tempWithTotal = lineWith + lineHeight + lp.leftMargin + lp.rightMargin

        expandView?.layoutParams = lp

        if (tempWithTotal > screenWith) {

            lastlinearLayout.removeViewAt(lastlinearLayout.childCount - 1)
            val removedWidth = cacheViewWith.get(totalSize - 1)
            lineWith = lineWith - removedWidth - (cacheViewArray.get(totalSize).layoutParams as LayoutParams).leftMargin - (cacheViewArray.get(totalSize).layoutParams as LayoutParams).rightMargin

            totalSize--

            lastlinearLayout.addView(expandView)
        } else {
            lastlinearLayout.addView(expandView)
        }
        canAddTag = false

    }

    private fun addCachedUnAddView() {
        // 添加剩余未添加的view
        for (i in totalSize until cacheViewArray.size()) {
            calcAndAddTag(cacheViewArray.get(i), cacheViewArray.get(i).layoutParams as LayoutParams)
        }
    }

    private fun removeMoreView() {
        val morelineLayout = getChildAt(childCount - 1) as LinearLayout
        val lineLastIndex = morelineLayout.childCount - 1
        morelineLayout.removeViewAt(lineLastIndex)
    }

    private fun calcAndAddTag(tagView: TextView, params: LayoutParams? = LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)) {
        val wMspec = MeasureSpec.makeMeasureSpec((1 shl 30) - 1, MeasureSpec.AT_MOST)
        val hMspec = MeasureSpec.makeMeasureSpec(UiUtils.dp2px(30), MeasureSpec.EXACTLY)
        tagView.measure(wMspec, hMspec)
        val measuredWidth: Int = tagView.measuredWidth
        val measuredHeight: Int = tagView.measuredHeight
        lineHeight = measuredHeight

        val tempWithTotal = lineWith + measuredWidth + (tagView.layoutParams as LayoutParams).leftMargin + (tagView.layoutParams as LayoutParams).rightMargin

        if (childCount == 0 || tempWithTotal > screenWith) {
            // 已有的line放置不下，新增一行
            if (!canAddTag) {
                return
            }
            if (!isExpand && maxLines > 0 && maxLines == childCount) {
                canAddTag = false
                return
            }
            val linearlayout = createHorizontalLinearLayout()
            addView(linearlayout, LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT))
            linearlayout.addView(tagView, params)
            cacheViewWith.put(totalSize, measuredWidth)
            totalSize++
            val flexboxMarginLeft = params?.leftMargin ?: 0
            val flexboxMarginright = params?.rightMargin ?: 0

            lineWith = flexboxMarginLeft + flexboxMarginright + paddingLeft + paddingRight + measuredWidth + (tagView.layoutParams as LayoutParams).leftMargin + (tagView.layoutParams as LayoutParams).rightMargin
        } else {
            // 在原有行中添加view
            (getChildAt(childCount - 1) as LinearLayout).addView(tagView, params)
            cacheViewWith.put(totalSize, measuredWidth)
            totalSize++
            lineWith = tempWithTotal
        }
        lineWithArray.put(childCount - 1, lineWith)

    }

    private fun createHorizontalLinearLayout(): LinearLayout {
        return LinearLayout(context).apply { orientation = HORIZONTAL }
    }

    var lineWith = 0
    var lineWithArray: SparseIntArray = SparseIntArray()

    // 容器中tag的总数量，更多按钮不计算在内
    var totalSize = 0
    val screenWith: Int = UiUtils.getScreenWidth()
    var maxLines: Int = -1
    var canAddTag: Boolean = true

    // 折叠按钮是否展开
    var isExpand: Boolean = false
    var expandView: View? = null
    var lineHeight: Int = 0
    var cacheViewArray: SparseArray<TextView> = SparseArray()
    var cacheViewWith: SparseIntArray = SparseIntArray()

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes)

    init {
        orientation = VERTICAL
    }

    fun addTag(tagList: List<TagBean>) {
        var position = 0
        tagList.forEach {
            val tagView = createTagView(it)
            tagView.tag = position
            tagView.setOnClickListener {
                mTagViewClickListener?.invoke(tagView.tag as Int)
            }
            position ++
            addTagView(tagView)
        }
    }

    private fun createTagView(tagBean: TagBean): TextView {
        val tabView = TextView(context)
        tabView.textSize = 13f
        tabView.setTextColor(UiUtils.getColor(R.color.color_292933))
        val shapeDrawable = GradientDrawable()
        shapeDrawable.setColor(UiUtils.getColor(R.color.color_f7f7f8))
        shapeDrawable.cornerRadius = UiUtils.dp2px(2).toFloat()
        tabView.background = shapeDrawable
        tabView.maxLines = 1
        tabView.maxEms = 10
        tabView.ellipsize = TextUtils.TruncateAt.END
        tabView.gravity = Gravity.CENTER
        tabView.setPadding(UiUtils.dp2px(8), 0, UiUtils.dp2px(8), 0)
        // 增加历史搜索词前缀logo
        tabView.text = tagBean.keyword
        val layoutParams = LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, UiUtils.dp2px(30))
        layoutParams.leftMargin = UiUtils.dp2px(10)
        layoutParams.bottomMargin = UiUtils.dp2px(10)
        tabView.layoutParams = layoutParams
        return tabView
    }

    fun setTagViewClickListener(listener: (Int) -> Unit) {
        mTagViewClickListener = listener
    }

    class TagBean(
            var keyword: String?,
            var extendParam: Map<String, String>?
    )
}

