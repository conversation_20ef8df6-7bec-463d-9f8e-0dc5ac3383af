package com.ybmmarketkotlin.viewmodel

import android.content.Intent
import android.graphics.Typeface
import android.os.SystemClock
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.ybmmarket20.R
import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.cart.*
import com.ybmmarket20.bean.cart.CartBeanWraper.Companion.CLOSE_AN_ACCOUNT
import com.ybmmarket20.bean.cart.CartBeanWraper.Companion.CLOSE_AN_ACCOUNT_COUPON
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.network.request.CartRequest
import com.ybmmarket20.network.request.PayResultRequest
import com.ybmmarket20.network.request.SearchDataRequest
import com.ybmmarket20.network.request.SearchSpellGroupRequest
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 *
 *   购物车数据显示结构：
 *   - 自营公司
 *      - 自营活动
 *      - 自营活动商品
 *      - ...
 *      - 自营活动商品
 *      - 自营套餐
 *      - 自营套餐商品
 *      - ...
 *      - 自营套餐商品
 *      - 自营店铺
 *      - 自营店铺商品
 *      - ...
 *      - 自营店铺商品
 *   - pop公司
 *      - pop商品
 *   - 失效头部
 *   - 失效商品
 *   - ....
 *   - 失效套餐
 *   - 失效套餐商品
 *   - ...
 *   - 失效套餐商品
 */
class CartViewModel : ViewModel() {
    val cartBean = MutableLiveData<CartBeanWraper>()
    val settleBean = MutableLiveData<SettleBean>()
    val onlyStatusResultBean = MutableLiveData<Boolean>()
    val onlyGoodStatusResultBean = MutableLiveData<Boolean>()
    val batchcollectResultBean = MutableLiveData<BaseBean<String>>()
    val batchCollectWithDataInfoLiveData = MutableLiveData<BaseBean<CollectionInvalidGoodsInfo>>()
    val recommendData = MutableLiveData<RefreshWrapperPagerBean<RowsBean>>()
    val showToastLiveData = MutableLiveData<String>()

    /**
     * 获取购物车数据
     */
    fun getCartData(merchantId: String): Unit {
        viewModelScope.launch {
            val result = CartRequest().getCartData(merchantId)
            // TODO: 2021/11/9 这里做数据转换
            val cartBeanWraper = CartBeanWraper()
            cartBeanWraper.apply {
                result?.data?.let {
                    // 1. 构造一级店铺头部数据
                    // 1.1 构造二级 自营店铺商品数据

                    // 1.2 构造二级 活动头部
                    // 1.3 构造二级 活动商品头部
                    // 1.4 构造二级 活动商品中间
                    // 1.5 构造二级 活动商品尾部

                    // 1.6 构造二级 套餐商品头部
                    // 1.7 构造二级 套餐商品
                    // 1.8 构造二级 套餐尾部

                    // 2. 构造一级店铺尾部数据
                    // 3.构造一级失效头部数据
                    it.company?.forEach { cartCompanyBean ->
                        val Level0ItemShopHeaderBean = Level0ItemShopHeaderBean().apply {
                            shopName = cartCompanyBean?.companyName
                            orgId = cartCompanyBean?.orgId
                            if (cartCompanyBean.isSelfCompany()) {
                                isThirdCompany = false
                                shopCode = cartCompanyBean?.mainShopCode

                                val ybmShop = cartCompanyBean.shop?.filter { it.shopType == "ybm" }?.firstOrNull()
                                originalShopCode = ybmShop?.originalShopCode
                                isHaveVoucher = ybmShop?.isHaveVoucher == 1
                                if (isHaveVoucher) {
                                    skuids = getShopSkuIds(ybmShop).toString()
                                }

                                showReturnVoucherInfo = ybmShop?.returnVoucherInfo?.isMatch == 1
                                returnVoucherTips = ybmShop?.returnVoucherInfo?.text
                                returnVoucherJumpUrl = ybmShop?.returnVoucherInfo?.action
                                returnVoucherUrlText = "去凑单"

                            } else {
                                isThirdCompany = true
                                val popShop = cartCompanyBean.shop.firstOrNull()
                                originalShopCode = popShop?.originalShopCode
                                shopCode = popShop?.shopCode
                                isHaveVoucher = popShop?.isHaveVoucher == 1
                                if (isHaveVoucher) {
                                    skuids = getShopSkuIds(popShop).toString()
                                }

                                showReturnVoucherInfo = popShop?.returnVoucherInfo?.isMatch == 1
                                returnVoucherTips = popShop?.returnVoucherInfo?.text
                                returnVoucherJumpUrl = popShop?.returnVoucherInfo?.action
                                returnVoucherUrlText = "去凑单"

                            }
                            activityId = cartCompanyBean?.activityId
                            activityType = cartCompanyBean?.activityType

                            //
//                            var showReturnVoucherInfo :Boolean = false
//                            var returnVoucherTips :String? = null
//                            var returnVoucherJumpUrl :String? = null
//                            var returnVoucherUrlText :String? = null

                            selected = cartCompanyBean?.selectStatus == 1
                            shopJumpUrl = cartCompanyBean?.shopJumpUrl

                            showFreightIcon = cartCompanyBean?.freightIconShowStatus == 1
                            showFreightTips = cartCompanyBean?.freightTipsShowStatus == 1
                            freightTips = cartCompanyBean?.freightTips
                            freightJumpUrl = cartCompanyBean?.freightJumpUrl
                            freightUrlText = cartCompanyBean?.freightUrlText ?: ""

                            // 构造二级数据
                            addSubItemForCompany(cartCompanyBean, this)
                        }
                        // shopheader
                        this.cartEntityList.add(Level0ItemShopHeaderBean)
                        // shopfooter
                        this.cartEntityList.add(Level0ItemShopFooterBean().apply {
                            payAmount = cartCompanyBean.payAmount
                            productVarietyNum = cartCompanyBean.productVarietyNum
                            productTotalNum = cartCompanyBean.productTotalNum
                        })
                    }
                    it.novalidGroup?.let {
                        this.cartEntityList.add(Level0ItemInvalidBean().apply {
                            title = it.title
                            productTotalNum = it.productTotalNum
                        })
                        // 构造二级数据
                        addSubItemForInvalid(it, this.cartEntityList)
                    }
                }
            }
            cartBeanWraper.apply {
                discountsStr =
                    "促销减:¥${UiUtils.transform(result?.data?.rePrice?.toString())} 用券减:¥${UiUtils.transform(UiUtils.transform(result?.data?.voucherDiscountAmount?.toString()))}"
                totalAmount = "总计：¥${UiUtils.transform(result?.data?.payAmount?.toString())}"
                isSelected = result?.data?.selectStatus == 1
                commitText = if (result?.data?.hasVouchersNotReceived == 1) CLOSE_AN_ACCOUNT_COUPON else CLOSE_AN_ACCOUNT
                selectNum = result?.data?.selectNum ?: 0
                varietyNum = result?.data?.varietyNum ?: 0
                promoAmountDto = result?.data?.promoAmountDto
                crossStoreVoucherDto = result?.data?.crossStoreVoucherDto
                canSettle = result?.data?.canSettle ?: 0
                unsatisfiedStartPriceList = result?.data?.unsatisfiedStartPriceList
                unsatisfiedFreeShippingList = result?.data?.unsatisfiedFreeShippingList
                specialTipsShow = result?.data?.specialProductTipsShowStatus == 1
            }
            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_SHOPNUMBER))
            cartBean.postValue(cartBeanWraper)
        }
    }

    private fun getShopSkuIds(ybmShop: CartShopList?): StringBuilder {
        val skuidsStringBuilder = StringBuilder()
        ybmShop?.shoppingGroupFrontDtos?.forEach {
            it?.sorted?.forEach {
                if (it.itemType == 3) {
                    it.subItemList.forEach {
                        skuidsStringBuilder.append(it.skuId).append(",")
                    }
                } else {
                    skuidsStringBuilder.append(it.item.skuId).append(",")
                }
            }
        }
        skuidsStringBuilder.takeIf { it.endsWith(",") }?.apply { deleteCharAt(this.lastIndex) }
        return skuidsStringBuilder
    }

    /**
     * 修改商品的选中状态
     */
    fun changeGoodsSelectStatus(check: Boolean, itemId: String, isGroup: Boolean = false) {
        viewModelScope.launch {
            var result: BaseBean<AbstractChangeCart>? = null
            val paramMap = hashMapOf<String, String>().apply {
                put("merchantId", SpUtil.getMerchantid())
            }
            if (isGroup) {
                paramMap.put("packageId", itemId)
            } else {
                paramMap.put("skuId", itemId)
            }
            if (check) {
                result = CartRequest().selectItem(paramMap)
            } else {
                result = CartRequest().cancelItem(paramMap)
            }
            onlyGoodStatusResultBean.postValue(result?.isSuccess)
        }
    }

    /**
     * 修改店铺的选中状态
     */
    fun changeShopSelectStatus(check: Boolean, itemId: String, isThirdCompany: Boolean) {
        viewModelScope.launch {
            var result: BaseBean<AbstractChangeCart>? = null
            val paramMap = hashMapOf<String, String>().apply {
                put("merchantId", SpUtil.getMerchantid())
            }
            paramMap.put("orgId", itemId)
            paramMap.put("isThirdCompany", if (isThirdCompany) "1" else "0")
            if (check) {
                result = CartRequest().selectAllShopItem(paramMap)
            } else {
                result = CartRequest().cancelAllShopItem(paramMap)
            }
            onlyGoodStatusResultBean.postValue(result?.isSuccess)
        }
    }


    /**
     * 修改购物车所有商品的选中状态
     */
    fun changeAllSelectStatus(check: Boolean) {
        viewModelScope.launch {
            var result: BaseBean<AbstractChangeCart>? = null
            val paramMap = hashMapOf<String, String>().apply {
                put("merchantId", SpUtil.getMerchantid())
            }
            if (check) {
                result = CartRequest().selectAllShopItem(paramMap)
            } else {
                result = CartRequest().cancelAllShopItem(paramMap)
            }
            onlyGoodStatusResultBean.postValue(result?.isSuccess)
        }
    }


    /**
     *  修改购物车商品的数量
     */
    fun changeCart(paramsMap: Map<String, String>) {
        viewModelScope.launch {
            val result: BaseBean<CartDataBean>? = CartRequest().changeCart(paramsMap)
            if (result?.isSuccess == true) {

                val skuid = paramsMap.get("skuId")
                val packageId = paramsMap.get("skuId")
                if (!skuid.isNullOrEmpty()) {
                    HandlerGoodsDao.getInstance().updateItem(skuid.toLong(), result.data.qty, false)
                }
                if (!packageId.isNullOrEmpty()) {
                    HandlerGoodsDao.getInstance().updateItem(packageId.toLong(), result.data.qty, true)
                }
            }
            onlyGoodStatusResultBean.postValue(result?.isSuccess)
        }
    }

    /**
     * 从购物车中删除商品
     */
    fun removeProductFromCart(merchantId: String, packageIds: String, ids: String, isInvalidPackage: Boolean = false) {
        viewModelScope.launch {
            val result: BaseBean<EmptyBean>? = CartRequest().removeProductFromCart(merchantId, packageIds, ids)
            if (result?.isSuccess == true) {
                HandlerGoodsDao.getInstance().deleteItems(packageIds?.split(","))
                HandlerGoodsDao.getInstance().deleteItems(ids?.split(","))
                if (isInvalidPackage) showToastLiveData.postValue("收藏成功")
            }

            onlyStatusResultBean.postValue(result?.isSuccess)
        }
    }

    /**
     * 收藏商品
     */
    fun batchcollect(ids: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(ids)
            result?.let {
                val idsBaseBean = BaseBean(it, ids)
                idsBaseBean.let(batchcollectResultBean::postValue)
            }
        }
    }

    /**
     * 收藏并删除失效套餐
     */
    fun batchCollectAndRemoveInvalidPackage(collectionInvalidGoodsInfo: CollectionInvalidGoodsInfo) {
        viewModelScope.launch(Dispatchers.IO) {
            val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(collectionInvalidGoodsInfo.ids?: "")
            result?.let {
                if (result.isSuccess.not()) {
                    val idsBaseBean = BaseBean(it, collectionInvalidGoodsInfo.ids?: "")
                    idsBaseBean.let(batchcollectResultBean::postValue)
                } else {
                    removeProductFromCart(SpUtil.getMerchantid(), collectionInvalidGoodsInfo.packageId, "", true)
                }
            }
        }
    }

    /**
     * 失效商品收藏后从购物车移除(失效套餐单独处理)
     */
    fun batchCollectWithGoodsInfo(ids: String, goodList: MutableList<MultiItemEntity>?) {
        viewModelScope.launch(Dispatchers.IO) {
            val idArray = ids.split(",")
            if (idArray.size > 1) {
                // 所有无效商品移入收藏
                val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(ids)
                val goodsIds = mutableListOf<String>()
                val packageIds = mutableListOf<String>()
                goodList?.forEach {
                    if (it is Level1InvalidGoodBean) {
                        goodsIds.add(it.skuid ?: "")
                    }
                    if (it is Level1InvalidGroupHeaderBean) {
                        packageIds.add(it.packageId ?: "")
                    }
                }
                if (result?.isSuccess?.not() == true) {
                    batchCollectWithDataInfoLiveData.postValue(BaseBean<CollectionInvalidGoodsInfo>(result, null))
                } else {
                    batchCollectWithDataInfoLiveData.postValue(BaseBean.newSuccessBaseBean(
                        CollectionInvalidGoodsInfo(INVALID_GOODS_ALL, packageIds.joinToString(","), goodsIds.joinToString(","))
                    ))
                }
                return@launch
            } else {
                // 单个无效商品
                val findResult = goodList?.filterIsInstance<Level1InvalidGoodBean>()
                    ?.find { it.skuid === ids }
                if (findResult != null) {
                    //失效商品（非套餐商品）
                    val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(ids)
                    if (result?.isSuccess?.not() == true) {
                        batchCollectWithDataInfoLiveData.postValue(BaseBean<CollectionInvalidGoodsInfo>(result, null))
                    } else {
                        batchCollectWithDataInfoLiveData.postValue(BaseBean.newSuccessBaseBean(CollectionInvalidGoodsInfo(
                            INVALID_GOODS_NORMAL, "", ids)))
                    }
                    return@launch
                }
                // 失效商品套餐 不收藏直接返回，接收数据后处理数据
                val groupGoodsList = goodList?.filterIsInstance<Level1InvalidGroupGoodBean>()
                val packageId = groupGoodsList?.find { it.skuid == ids }?.packageId
                val goodsInvalidPackageList = groupGoodsList?.filter { it.packageId == packageId && it.skuid != null }
                val idsList = goodsInvalidPackageList?.map { it.skuid?: "" }
                val idsListMutable = idsList?.toMutableList()
                val packageGroupGoodsIds = idsListMutable?.joinToString(",")
                batchCollectWithDataInfoLiveData.postValue(BaseBean.newSuccessBaseBean(CollectionInvalidGoodsInfo(
                    INVALID_GOODS_PACKAGE, packageId?: "", packageGroupGoodsIds)))
            }
        }
    }


    /**
     * 推荐商品
     */
    fun getRecommendGoodlist(params: Map<String, String>) {
        viewModelScope.launch {
            val recommendData = SearchDataRequest().getRecommendSearchDataRequest(params)
            <EMAIL>(recommendData?.data)
        }
    }


    /**
     * 领取优惠券跳转倒凑单页
     */
    fun getVoucher(voucherTemplateId: String?, chooseUrl: String?) {
        viewModelScope.launch {
            if (!TextUtils.isEmpty(voucherTemplateId)) {
                val paramsMap = mutableMapOf<String, String>()
                paramsMap["merchantId"] = SpUtil.getMerchantid()
                paramsMap["voucherTemplateId"] = voucherTemplateId!!
                var result: BaseBean<EmptyBean>? = CartRequest().getVoucher(paramsMap)
                // todo 这里的跳转路由可能有问题
                if (result?.isSuccess == true) {
                    chooseUrl?.let { RoutersUtils.open(chooseUrl) }
                }
                onlyStatusResultBean.postValue(result?.isSuccess)
            } else {
                chooseUrl?.let { RoutersUtils.open(chooseUrl) }
            }
        }
    }

    /**
     * 生成预订单
     */
    fun preSettle(notSubmitOrderOrgIds: String?) {
        viewModelScope.launch {
            val paramsMap = mutableMapOf<String, String>()
            paramsMap["merchantId"] = SpUtil.getMerchantid()
            notSubmitOrderOrgIds?.let {
                paramsMap["notSubmitOrderOrgIds"] = it
                ToastUtils.showLong("不够起送价的店铺商品将不能参与订单提交，请注意结算页订单金额和优惠变化")
            }
            val result: BaseBean<SettleBean>? = CartRequest().preSettle(paramsMap)
            result?.data?.let {
                var openUrl: StringBuilder? = StringBuilder("ybmpage://payment?tranNo=${it.tranNo}&${IntentCanst.JG_ENTRANCE}=购物车")
                notSubmitOrderOrgIds?.let {
                    openUrl?.append("&&notSubmitOrderOrgIds=${notSubmitOrderOrgIds}")
                }
                RoutersUtils.open(openUrl.toString())
            }
            onlyStatusResultBean.postValue(result?.isSuccess)
        }
    }


    private fun addSubItemForInvalid(it: CartShoppingGroupFrontBean, level0ItemInvalidBean: MutableList<MultiItemEntity>) {
        it.sorted?.forEach {

            val headItem = it.item
            if (it.itemType == 3) {
                // 添加失效套餐头部
                level0ItemInvalidBean.add(Level1InvalidGroupHeaderBean().apply {
                    price = "${headItem?.name}: ¥${headItem?.price}"
                    origPrice = "原价: ¥${headItem?.origPrice}"
                    packageId = "${headItem?.packageId ?: ""}"
                })
                it.subItemList?.forEach {cattItemBean ->
                    // 添加失效套餐商品
                    level0ItemInvalidBean.add(Level1InvalidGroupGoodBean("${cattItemBean.packageId}").apply {
                        generateInvalidGoodsData(cattItemBean, true)
                    })
                }
            } else {
                // 添加失效单个商品
                level0ItemInvalidBean.add(Level1InvalidGoodBean().apply {
                    generateInvalidGoodsData(headItem)
                })
            }

        }

    }

    private fun Level1InvalidItemGoodsBeanAbs.generateInvalidGoodsData(itemBean: CartItemBean, isInvalidGroup: Boolean = false) {
        name = SpannableStringBuilder("${itemBean.name} / ${itemBean.spec}").apply {
            itemBean.spec?.let {
                setSpan(AbsoluteSizeSpan(12, true), this.length - it.length, this.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            }
        }
        imageUrl = itemBean.imageUrl
        markerUrl = itemBean.markerUrl
        groupGoodsNum = if (isInvalidGroup) "X${itemBean.amount}" else ""
        skuid = "${itemBean?.skuId}"

        invalidStatus = itemBean.stockTitle ?: "失效"
        invalidContent = itemBean.loseTagText
//        invalidContent = when (itemBean.skuStatus) {
//            2 -> "该商品已售罄"
//            4 -> "该商品已下架"
//            91 -> "暂无购买权限"
//            92 -> "该商品已限购"
//            95 -> "该商品超出经营范围"
//            105 -> "价格签署协议可见"
//            else -> ""
//        }

    }

    private fun addSubItemForCompany(originData: CartCompanyBean?, level0Item: Level0ItemShopHeaderBean) {
        // 提取自营店铺的商品出来
        originData?.shop?.filter { it.shopType == "ybm" }?.firstOrNull()?.let { cartshoppingBean ->
            cartshoppingBean.shoppingGroupFrontDtos?.forEach {
                it.shopCode = cartshoppingBean.shopCode
                addSubShopItem(it, level0Item)
            }
        }
        originData?.shop?.filter { it.shopType != "ybm" }?.forEach { cartshoppingBean ->
            val level1ItemSubShopHeaderBean = Level1ItemSubShopHeaderBean().apply {
                companyName = cartshoppingBean.shopName
                shopCode = cartshoppingBean.shopCode
                shopJumpUrl =
                    if (cartshoppingBean?.appLinkUrl?.startsWith("ybmpage") == false) "ybmpage://commonh5activity?url=${cartshoppingBean?.appLinkUrl}" else cartshoppingBean?.appLinkUrl
                isHaveVoucher = cartshoppingBean.isHaveVoucher == 1
                selected = cartshoppingBean.selectStatus == 1

                if (isHaveVoucher) {
                    val skuidsStringBuilder = StringBuilder()
                    cartshoppingBean.shoppingGroupFrontDtos.forEach {
                        it?.shopCode = cartshoppingBean.shopCode
                        it?.sorted?.forEach {
                            if (it.itemType == 3) {
                                it.subItemList.forEach {
                                    skuidsStringBuilder.append(it.skuId).append(",")
                                }
                            } else {
                                skuidsStringBuilder.append(it.item.skuId).append(",")
                            }
                        }
                    }
                    skuidsStringBuilder.takeIf { it.endsWith(",") }?.apply { deleteCharAt(this.lastIndex) }
                    skuids = skuidsStringBuilder.toString()
                }


            }
            // 添加自然人店铺头
            if (originData.isSelfCompany()) level0Item.addSubItem(level1ItemSubShopHeaderBean)
            // 提取自然人或pop店铺商品出来
            cartshoppingBean.shoppingGroupFrontDtos.forEach {
                addSubShopItem(it, level0Item, originData.isSelfCompany())
            }

        }


    }

    private fun addSubShopItem(cartShoppingGroupFrontBean: CartShoppingGroupFrontBean, level0Item: Level0ItemShopHeaderBean, isPersonalShop: Boolean = false) {
        if (!TextUtils.isEmpty(cartShoppingGroupFrontBean.title)) {
            // 提取活动商品出来
            // 组合活动添加活动头、单品活动添加单品
//             todo 把这里得条件换掉
            when (cartShoppingGroupFrontBean.combinationType) {
//            when (cartShoppingGroupFrontBean.sorted?.size) {
                1 -> extractCommonBeanWithActivy(level0Item, cartShoppingGroupFrontBean)
                else -> extractActivityBean(level0Item, cartShoppingGroupFrontBean)
            }

        } else {
            // 提取非活动商品出来
            cartShoppingGroupFrontBean?.sorted?.forEach {
                // 非活动的单品或者套餐
                when (it.itemType) {
                    3 -> {
                        //  套餐商品
                        // 添加套餐头
                        // 添加套餐商品
                        // 添加套餐尾
                        extractGroupBean(level0Item, cartShoppingGroupFrontBean)
                    }
                    else -> {
                        // 非活动单品
                        createGoodsBean(it, level0Item, false, false, isPersonalShop)
                    }
                }
            }
        }
    }

    /**
     * 提取可购套餐数据
     */
    private fun extractGroupBean(level0Item: Level0ItemShopHeaderBean, cartShoppingGroupFrontBean: CartShoppingGroupFrontBean) {
        // 套餐商品
        // 添加套餐头
        val headItem = cartShoppingGroupFrontBean.sorted?.firstOrNull()?.item

        level0Item.addSubItem(Level1ItemGroupHeaderBean().apply {
            selected = headItem?.status == 1
            price = "${headItem?.name}: ¥${UiUtils.transform(headItem?.price?.toString())}"
            origPrice = "原价: ¥${UiUtils.transform(headItem?.origPrice?.toString())}"
            packageId = "${headItem?.packageId ?: ""}"
        })
        // 添加套餐商品
        cartShoppingGroupFrontBean.sorted?.firstOrNull()?.subItemList?.forEach {
            level0Item.addSubItem(Level1ItemGroupGoodBean().apply {
                generateBaseGoodsData(this, it)
                packageProductQty = it.packageProductQty
            })
        }
        // 添加套餐尾
        level0Item.addSubItem(Level1ItemGroupFooterBean().apply {
            subtotal = "小计 ¥${UiUtils.transform(headItem?.realPayAmount?.toString())}"
            amount = "${headItem?.amount ?: 0}"
            packageId = headItem?.packageId?.toString()
            canSplit = headItem?.isSplit == 1
            mediumPackageNum = headItem?.mediumPackageNum ?: 1
        })
    }

    /**
     * 提取单品促销数据
     */
    private fun extractCommonBeanWithActivy(
        level0Item: Level0ItemShopHeaderBean,
        cartShoppingGroupFrontBean: CartShoppingGroupFrontBean
    ) {
        val commonGoodsBean = Level1ItemCommonGoodsBean().apply {
            generateBaseGoodsData(this, cartShoppingGroupFrontBean?.sorted?.firstOrNull()?.item)
            activityBean = Level1ItemActivityHeaderBean().apply {
                title = cartShoppingGroupFrontBean.title
                titleUrl = cartShoppingGroupFrontBean.titleUrl
                titleUrlText = cartShoppingGroupFrontBean.titleUrlText
                type = cartShoppingGroupFrontBean.type
                combinationType = cartShoppingGroupFrontBean.combinationType
                activityTypeText = cartShoppingGroupFrontBean.activityTypeText
            }
        }
        level0Item.addSubItem(commonGoodsBean)
    }

    private fun generateBaseGoodsData(baseGoods: Level1ItemGoodsBeanAbs, originData: CartItemBean?) {
        baseGoods.apply {
            skuid = originData?.skuId?.toString()
            imageUrl = originData?.imageUrl
            markerUrl = originData?.markerUrl
            val nameStringBuilder = SpannableStringBuilder("${originData?.name}")
            spec = originData?.spec
            spec?.let {
                nameStringBuilder.append("/")
                nameStringBuilder.append(spec)
                nameStringBuilder.setSpan(
                    AbsoluteSizeSpan(12, true),
                    nameStringBuilder.length - it.length - 1,
                    nameStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                nameStringBuilder.setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(YBMAppLike.getAppContext(), R.color.color_9494A6)),
                    nameStringBuilder.length - it.length - 1,
                    nameStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                nameStringBuilder.setSpan(
                    StyleSpan(Typeface.NORMAL),
                    nameStringBuilder.length - it.length - 1,
                    nameStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
            }
            name = nameStringBuilder
            if (!originData?.stockTitle.isNullOrEmpty() && originData?.valid == 1) {
                invalidStatus = originData?.stockTitle
            } else {
                invalidStatus = null
            }
            val priceStringBuilder = SpannableStringBuilder("¥${UiUtils.transform(originData?.price ?: 0.00)}")
            originData?.sku?.productUnit?.let {
                priceStringBuilder.append("/")
                priceStringBuilder.append(originData?.sku?.productUnit)
                priceStringBuilder.setSpan(
                    AbsoluteSizeSpan(10, true),
                    priceStringBuilder.length - it.length - 1,
                    priceStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                priceStringBuilder.setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(YBMAppLike.getAppContext(), R.color.color_9494A6)),
                    priceStringBuilder.length - it.length - 1,
                    priceStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                priceStringBuilder.setSpan(
                    StyleSpan(Typeface.NORMAL),
                    priceStringBuilder.length - it.length - 1,
                    priceStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
            }
            price = priceStringBuilder

            effect = originData?.nearEffect
            actPurchaseTip = originData?.actPurchaseTip
            tagList = originData?.tagList
            if (UiUtils.transform(originData?.showPriceAfterDiscount ?: "0.00").toDouble() > 0) {
                showPriceAfterDiscount = "折后约¥${UiUtils.transform(originData?.showPriceAfterDiscount)}"
            }
            dataTagList = originData?.dataTagList
            subtotal = SpannableStringBuilder("小计 ¥${UiUtils.transform(originData?.realPayAmount?.toString() ?: "")}")
            amount = "${originData?.amount ?: 0}"
            var showpurchaseLimitStr = originData?.getPromoQty() ?: 0 > 0 && originData?.getNormalQty() ?: 0 > 0 && originData?.getSku() != null
            purchaseLimitStr = if (showpurchaseLimitStr) "此商品为限购商品，超出${originData?.promoQty}${originData?.sku?.productUnit}的部分将按原价购买" else null
            originData?.showPriceAfterDiscount?.takeIf { showpurchaseLimitStr }?.let {
                purchaseLimitStr = "此商品为限购商品，超出${originData?.promoQty}${originData?.sku?.productUnit}的部分将按照原价购买，超出部分也参与折后价计算"
            }
            seckillRemainingTime = originData?.seckillRemainingTime ?: 0
            responseLocalTime = SystemClock.elapsedRealtime()
            selected = originData?.status == 1
            mediumPackageNum = originData?.mediumPackageNum ?: 1
            canSplit = originData?.isSplit == 1
            tagList = originData?.tagList
            dataTagList = originData?.dataTagList
        }
    }

    /**
     *  提取组合促销头数据、促销中间商品数据、促销尾部数据
     */
    private fun extractActivityBean(
        level0Item: Level0ItemShopHeaderBean,
        cartShoppingGroupFrontBean: CartShoppingGroupFrontBean
    ) {
        level0Item.addSubItem(Level1ItemActivityHeaderBean().apply {
            title = cartShoppingGroupFrontBean.title
            titleUrl = cartShoppingGroupFrontBean.titleUrl
            titleUrlText = cartShoppingGroupFrontBean.titleUrlText
            type = cartShoppingGroupFrontBean.type
            combinationType = cartShoppingGroupFrontBean.combinationType
            activityTypeText = cartShoppingGroupFrontBean.activityTypeText
            shopCode = cartShoppingGroupFrontBean.shopCode
            activityId = cartShoppingGroupFrontBean.activityId ?: ""
            activityType = cartShoppingGroupFrontBean.activityType ?: ""
        })
        // 添加活动非最后一个商品
        cartShoppingGroupFrontBean?.sorted?.dropLast(1)?.forEach {
            createGoodsBean(it, level0Item, true)
        }
        // 添加活动最后一个商品
        cartShoppingGroupFrontBean?.sorted?.lastOrNull()?.let {
            createGoodsBean(it, level0Item, isActivity = true, isLast = true)
        }
    }

    private fun createGoodsBean(
        it: CartSortedNewBean,
        level0Item: Level0ItemShopHeaderBean,
        isActivity: Boolean = false,
        isLast: Boolean = false,
        isPersonalShop: Boolean = false
    ) {
        if (isActivity && isLast) level0Item.addSubItem(Level1ItemActivityGoodEndBean().apply {
            generateBaseGoodsData(this, it.item)
        })
        else if (isActivity && !isLast) level0Item.addSubItem(Level1ItemActivityGoodBean().apply {
            generateBaseGoodsData(this, it.item)
        })
        else level0Item.addSubItem(Level1ItemCommonGoodsBean().apply {
            this.isPersonalShop = isPersonalShop
            generateBaseGoodsData(this, it.item)
            activityBean = Level1ItemActivityHeaderBean().apply {
                title = it.item.title
                titleUrl = it.item.titleUrl
                titleUrlText = it.item.titleUrlText
                type = it.item.type
                combinationType = it.item.combinationType
                activityTypeText = it.item.activityTypeText
            }
        })
    }


}