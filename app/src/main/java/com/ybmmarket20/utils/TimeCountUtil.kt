package com.ybmmarket20.utils

import android.os.CountDownTimer
import android.widget.TextView

/**
 * @author: yuhaibo
 * @time: 2019-08-15 14:20.
 * projectName: ybm-android.
 * Description: 倒计时工具类
 *
 *
 * @param millisInFuture    倒计时总时长
 * @param countDownInterval 倒计时单位 毫秒.
 */
class TimeCountUtil(private val textView: TextView?, millisInFuture: Long, countDownInterval: Long) :
    CountDownTimer(millisInFuture, countDownInterval) {
    private var tickText = "s"
    private var finishText = "重新发送"

    override fun onTick(millisUntilFinished: Long) {
        textView?.isEnabled = false
        textView?.text = (millisUntilFinished / 1000).toString() + tickText
        mCountdownListener?.onCountdownIngListener(textView, (millisUntilFinished / 1000).toString())
    }

    override fun onFinish() {
        textView?.isEnabled = true
        textView?.text = finishText
        mCountdownListener?.onCountdownFinishListener(textView)
    }

    /**
     * 设置倒计时过程中button显示内容
     *
     * @param tickText 默认 s后重新获取
     */
    fun setTickText(tickText: String) {
        this.tickText = tickText
    }

    /**
     * 设置倒计时结束button显示内容
     *
     * @param finishText 默认 重新获取
     */
    fun setFinishText(finishText: String) {
        this.finishText = finishText
    }

    fun setCountdownListener(mCountdownListener: CountdownListener?): TimeCountUtil {
        this.mCountdownListener = mCountdownListener
        return this
    }

    private var mCountdownListener: CountdownListener? = null

    interface CountdownListener {
        fun onCountdownIngListener(view: TextView?, secondsNum: String)
        fun onCountdownFinishListener(view: TextView?)
    }

}
