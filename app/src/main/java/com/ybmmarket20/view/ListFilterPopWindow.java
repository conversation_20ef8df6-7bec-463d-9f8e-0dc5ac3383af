package com.ybmmarket20.view;

import android.graphics.Typeface;
import android.view.View;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.utils.RoutersUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用列表筛选pop
 */
public class ListFilterPopWindow extends BaseFilterPopWindow {
    private int currFirstPosition = 0;
    protected CommonRecyclerView listView;
    protected List<SearchFilterBean> list;
    protected YBMBaseAdapter adapter;
    protected boolean loadMore = false;//不用从网络获取数据

    @Override
    protected int getLayoutId() {
        return R.layout.pop_layout_list_filter;
    }

    @Override
    protected void initView() {
        listView = getView(R.id.cv_list_view);
        if (listView == null) {
            BugUtil.sendBug(new NullPointerException("listView == null"));
            RoutersUtils.rebootApp();
            return;
        }
        listView.setShowAutoRefresh(loadMore);
        listView.setEnabled(loadMore);
        adapter = new YBMBaseAdapter<SearchFilterBean>(R.layout.pop_layout_filter_list_item, list) {
            @Override
            protected void bindItemView(final YBMBaseHolder holder, final SearchFilterBean bean) {
                holder.setOnClickListener(R.id.ll_item_root, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        int last = currFirstPosition;
                        currFirstPosition = holder.getAdapterPosition();
                        if (mOnSelectListener != null) {
                            mOnSelectListener.getValue(bean);
                        }
                        notifyItemChanged(currFirstPosition);
                        notifyItemChanged(last);
                        dismiss();
                    }
                }).setText(R.id.tv_name, bean.realName);
                if (currFirstPosition == holder.getAdapterPosition()) {
                    holder.getView(R.id.tv_name).setActivated(true);
                    ((TextView) holder.getView(R.id.tv_name)).setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                } else {
                    holder.getView(R.id.tv_name).setActivated(false);
                    ((TextView) holder.getView(R.id.tv_name)).setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                }
            }
        };

        listView.setAdapter(adapter);
    }

    public void setCurrFirstPosition(int position){
        this.currFirstPosition = position;
    }

    public void setNewData(List<SearchFilterBean> list) {
        this.list = list;
        if (adapter != null) {
            setLayoutParams(ConvertUtils.dp2px(40) * list.size() + ConvertUtils.dp2px(10));
            adapter.setNewData(list);
        }
    }


    public static List<SearchFilterBean> getOrderStatus() {
        List<SearchFilterBean> list = new ArrayList<>();
        list.add(new SearchFilterBean(getOrderStatus(0), "0", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(1), "1", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(2), "2", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(3), "3", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(4), "4", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(6), "6", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(7), "7", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(10), "10", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(90), "90", list.size()));
        list.add(new SearchFilterBean(getOrderStatus(91), "91", list.size()));
        return list;
    }

    /**
     * 全部订单 0
     * 订单审核中 1
     * 订单配送中 2
     * 订单已完成 3
     * 订单已取消 4
     * 订单已拆单 6
     * 订单出库中 7
     * 订单未支付 10
     * 退款审核中 90
     * 订单已退款 91
     *
     * @param status
     * @return
     */
    public static String getOrderStatus(int status) {
        String str = "";
        switch (status) {
            case 0:
                str = "全部订单";
                break;
            case 1:
                str = "订单审核中";
                break;
            case 2:
                str = "订单配送中";
                break;
            case 3:
                str = "订单已完成";
                break;
            case 4:
                str = "订单已取消";
                break;
            case 5:
                str = "订单已删除";
                break;
            case 6:
                str = "订单已拆单";
                break;
            case 7:
                str = "订单出库中";
                break;
            case 9:
                str = "审核通过";
                break;
            case 10:
                str = "订单未支付";
                break;
            case 11:
                str = "订单已支付";
                break;
            case 90:
                str = "退款审核中";
                break;
            case 91:
                str = "退款完成";
                break;
            case 92:
                str = "退款失败";
                break;
            default:
                str = "订单已经完成";
                break;
        }
        return str;
    }

    public static List<SearchFilterBean> getPayType() {//1 在线支付 2货到付款
        List<SearchFilterBean> list = new ArrayList<>();
        list.add(new SearchFilterBean("全部渠道", "0", list.size()));
        list.add(new SearchFilterBean("在线支付", "1", list.size()));
        list.add(new SearchFilterBean("货到付款", "2", list.size()));
        list.add(new SearchFilterBean("线下转账", "3", list.size()));
        return list;
    }

    public static List<SearchFilterBean> getSort() {
        List<SearchFilterBean> list = new ArrayList<>();
        list.add(new SearchFilterBean("人气", "saleRank", list.size()));
        list.add(new SearchFilterBean("销量", "pro.pay_amount", list.size()));
        list.add(new SearchFilterBean("新品", "pro.create_time", list.size()));
        return list;
    }

    public static List<SearchFilterBean> getRanking(){
        List<SearchFilterBean> list = new ArrayList<>();
        list.add(new SearchFilterBean("综合排序", "smsr.sale_num", "综合排序", "comprehensive"));//人气
        list.add(new SearchFilterBean("销量从高到低", "spa.sale_num", "销量排序", "sale"));
        list.add(new SearchFilterBean("价格从低到高", "fob", "价格排序", "price_1"));
        list.add(new SearchFilterBean("价格从高到低", "fob", "价格排序", "price_2"));
        list.add(new SearchFilterBean("最新上架", "s.create_time", "最新上架", "new_sale"));
        return list;
    }

    /**
     * KA
     * @return
     */
    public static List<SearchFilterBean> getKaRanking(){
        List<SearchFilterBean> list = new ArrayList<>();
        list.add(new SearchFilterBean("综合排序", "smsr.sale_num", "综合排序"));//人气
        list.add(new SearchFilterBean("销量从高到低", "spa.sale_num", "销量排序"));
        list.add(new SearchFilterBean("最新上架", "s.create_time", "最新上架"));
        return list;
    }

    public static List<SearchFilterBean> getRanking4Plan(){
        List<SearchFilterBean> list = new ArrayList<>();
        list.add(new SearchFilterBean("综合排序", "", "综合排序"));//人气
        list.add(new SearchFilterBean("价格从低到高", "asc", "价格排序"));
        list.add(new SearchFilterBean("价格从高到低", "desc", "价格排序"));
        return list;
    }

    //商品状态（0,全部1：有货；2：缺货；3：无货）
    public static List<SearchFilterBean> getPlanScreen(){
        List<SearchFilterBean> list = new ArrayList<>();
        list.add(new SearchFilterBean("全部商品", "0", list.size()));//人气
        list.add(new SearchFilterBean("有货商品", "1", list.size()));
        list.add(new SearchFilterBean("无货商品", "3", list.size()));
        return list;
    }

}
