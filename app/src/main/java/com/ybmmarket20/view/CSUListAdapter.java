package com.ybmmarket20.view;

import static com.ybmmarket20.constant.AppNetConfig.LORD_BIDIMAGE;

import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.CSUDetailBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.widget.RoundConstraintLayout;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class CSUListAdapter extends BaseQuickAdapter<CSUDetailBean, CSUListAdapter.CSUViewHolder> {
    public  interface CSUItemEventListener{
        void onItemClick(String skuId);
        void onItemExposure(String skuId);
    }

    public interface CSUItemMoreClickListener {
        void onItemMoreClick();
    }

    private int mItemWidth;
    private CSUItemEventListener mListener;
    private CSUItemMoreClickListener mMoreItemClickListener;
    private int mBackgroundColor = Color.parseColor("#ffffff");

    private boolean mIsMainProductVirtualSupplier;

    public void setItemEventListener(CSUItemEventListener listener){
        mListener = listener;
    }

    public void setOnMoreItemListener(CSUItemMoreClickListener listener) { mMoreItemClickListener = listener; }

    public CSUListAdapter(int layoutResId) {
        super(layoutResId);
        mItemWidth = RelativeLayout.LayoutParams.MATCH_PARENT;
    }

    public void setItemBackground(int color){
        mBackgroundColor = color;
    }

    public void setIsMainProductVirtualSupplier(boolean isMainProductVirtualSupplier) {
        mIsMainProductVirtualSupplier = isMainProductVirtualSupplier;
    }

    @Override
    public void setNewData(@Nullable @org.jetbrains.annotations.Nullable List<CSUDetailBean> data) {
        super.setNewData(data);
        if(data != null && data.size() > 1){
            mItemWidth = UiUtils.getScreenWidth() * 7/10;
        } else {
            mItemWidth = RelativeLayout.LayoutParams.MATCH_PARENT;
        }
    }

    @NonNull
    @NotNull
    @Override
    public CSUViewHolder onCreateViewHolder(@NonNull @NotNull ViewGroup parent, int viewType) {
        CSUViewHolder holder = super.onCreateViewHolder(parent, viewType);
        holder.itemView.getLayoutParams().width = mItemWidth;
//        holder.itemView.setBackgroundColor(mBackgroundColor);
        return holder;
    }

    @Override
    protected void convert(@NonNull @NotNull CSUViewHolder csuViewHolder, CSUDetailBean csuDetailBean) {
        if (csuViewHolder.rclMore != null && csuViewHolder.tvMore != null) {
            if (csuDetailBean.getId() == null) {
                //更多
                csuViewHolder.rlRow.setVisibility(View.GONE);
                csuViewHolder.rclMore.setVisibility(View.VISIBLE);
                csuViewHolder.itemView.getLayoutParams().width = ViewGroup.LayoutParams.WRAP_CONTENT;
                return;
            } else {
                //商品
                csuViewHolder.rlRow.setVisibility(View.VISIBLE);
                csuViewHolder.rclMore.setVisibility(View.GONE);
                csuViewHolder.itemView.getLayoutParams().width = mItemWidth;
            }
        }
        Log.d("image_path", LORD_BIDIMAGE + csuDetailBean.getImageUrl());
        Glide.with(YBMAppLike.getAppContext()).load(LORD_BIDIMAGE + csuDetailBean.getImageUrl()).into(csuViewHolder.mImageView);
        csuViewHolder.mSkuName.setText(csuDetailBean.getShowName());
        if (!csuDetailBean.getNearEffect().isEmpty()){
            csuViewHolder.mSkuEffect.setVisibility(View.VISIBLE);
            csuViewHolder.mSkuEffect.setText("效期: "+csuDetailBean.getNearEffect());
        }else {
            csuViewHolder.mSkuEffect.setVisibility(View.GONE);
        }
        csuViewHolder.setBean(csuDetailBean);
        if(mListener != null){
            mListener.onItemExposure(csuDetailBean.getId());
        }
        if (TextUtils.isEmpty(csuDetailBean.getActPrice())) {
            csuViewHolder.llZengPinPrice.setVisibility(View.GONE);
        } else {
            csuViewHolder.llZengPinPrice.setVisibility(View.VISIBLE);
            SpannableStringBuilder priceBuilder = new SpannableStringBuilder("¥"+csuDetailBean.getActPrice());
            priceBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#FF2121")), 0, priceBuilder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            priceBuilder.setSpan(
                    new AbsoluteSizeSpan(10, true),
                    0,
                    1,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            );
            priceBuilder.append("/").append(csuDetailBean.getProductUnit());
            csuViewHolder.tvZengPinPrice.setText(priceBuilder);
            if (TextUtils.isEmpty(csuDetailBean.getPriceDesc())) {
                csuViewHolder.tvZengPinTip.setVisibility(View.GONE);
            } else {
                csuViewHolder.tvZengPinTip.setVisibility(View.VISIBLE);
            }
        }
        csuViewHolder.llZengPinPrice.setOnClickListener(v -> {
            if (TextUtils.isEmpty(csuDetailBean.getPriceDesc())) return;
            AlertDialogEx alert = new AlertDialogEx(mContext);
            alert.setTitle("价格说明")
                .setMessage(csuDetailBean.getPriceDesc())
                .setCancelButton("我知道了", (dialog, button) -> dialog.dismiss());
            alert.show();
        });
    }

    class CSUViewHolder extends BaseViewHolder{
        private final ImageView mImageView;
        private final TextView mSkuName;
        private final TextView mSkuEffect;
        private CSUDetailBean mBean;
        private LinearLayout llZengPinPrice;
        private TextView tvZengPinPrice;
        private ImageView tvZengPinTip;
        private LinearLayout llEffect;
        private ConstraintLayout rlRow;
        private RoundConstraintLayout rclMore;
        private TextView tvMore;

        public CSUViewHolder(View view) {
            super(view);
            rlRow = view.findViewById(R.id.rlRow);
            mImageView = view.findViewById(R.id.sku_image);
            mSkuName = view.findViewById(R.id.sku_name);
            mSkuEffect = view.findViewById(R.id.sku_effect);
            llZengPinPrice = view.findViewById(R.id.llZengPinPrice);
            tvZengPinPrice = view.findViewById(R.id.tvZengPinPrice);
            tvZengPinTip = view.findViewById(R.id.tvZengPinTip);
            llEffect = view.findViewById(R.id.llEffect);
            try {
                rclMore = view.findViewById(R.id.rclMore);
                tvMore = view.findViewById(R.id.tvMore);
            } catch (Exception e) {
                e.printStackTrace();
            }
            view.setOnClickListener(view1 -> {
                if(mBean != null){
                    String mIsMainProductVirtualSupplierStr = mIsMainProductVirtualSupplier? "1": "";
                    RoutersUtils.open("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + mBean.getId() + "&isMainProductVirtualSupplier=" + mIsMainProductVirtualSupplierStr);
                    if(CSUListAdapter.this.mListener != null){
                        CSUListAdapter.this.mListener.onItemClick(mBean.getId());
                    }
                }
            });
            rclMore.setOnClickListener(v -> {
                if (mMoreItemClickListener != null) mMoreItemClickListener.onItemMoreClick();
            });
        }

        public void setBean(CSUDetailBean bean){
            mBean = bean;
        }
    }
}
