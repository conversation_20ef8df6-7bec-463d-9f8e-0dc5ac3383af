package com.ybmmarket20.view;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import androidx.viewpager.widget.ViewPager;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.ybmmarket20.R;
import com.ybmmarket20.adapter.RecyclingPagerAdapter;
import com.ybmmarket20.bean.ModuleViewItem;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.util.List;

/**
 * // setAutoRoll(boolean autoRoll);
 * style 1位 时间  2位 布局样式(1 0)
 */
public class DynamicBannerLayout2 extends BaseDynamicLayout<ModuleViewItem> {
    public int defHeigth = 128;
    private ClipViewPager vp_arl;
    private List<ModuleViewItem> items;
    private boolean autoRoll;
    private MyPagerAdapter adapter;
    static Handler handler = new Handler();
    private final static int STEPTIME_DEF = 5000;
    private final static int STEPTIME_1 = 2000;
    private final static int STEPTIME_2 = 3000;
    private final static int STEPTIME_3 = 4000;
    private final static int STEPTIME_4 = 5000;
    private final static int STEPTIME_5 = 6000;
    private final static int STEPTIME_6 = 7000;
    private final static int STEPTIME_7 = 8000;
    private final static int STEPTIME_8 = 9000;
    private final static int STEPTIME_9 = 10000;
    private int stepTime = STEPTIME_DEF;
    boolean isRight = true;
    private RelativeLayout mRlLayout;
    protected int style;
    protected int layoutStyle;
    private LinearLayout ll_arl;
    protected boolean showDog;


    public DynamicBannerLayout2(Context context) {
        super(context);
    }

    public DynamicBannerLayout2(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicBannerLayout2(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        mRlLayout = (RelativeLayout) findViewById(R.id.rl_layout);
        vp_arl = (ClipViewPager) findViewById(R.id.vp_arl);
        ll_arl = (LinearLayout) findViewById(R.id.ll_arl);

        vp_arl.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_MOVE:
                    case MotionEvent.ACTION_DOWN:
                        setAutoRoll(false);
                        break;
                    case MotionEvent.ACTION_UP:
                        setAutoRoll(true);
                        break;
                }
                return false;
            }
        });
    }

    @Override
    public int getDefHeigth() {
        return defHeigth;
    }

    @Override
    public boolean supportSetHei() {
        return true;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_arl2;
    }

    @Override
    public void setItemData(List<ModuleViewItem> items) {
        this.items = items;
        // 处理viewpager
        adapter = new MyPagerAdapter();
        vp_arl.setAdapter(adapter);
        // 移除上次遗留的所有点
        if (showDog) {
            vp_arl.addOnPageChangeListener(PageListener);
            ll_arl.removeAllViews();
            //重新添加点
            addDots();
        }
        // 如果数据为空，onPageSelected(0)会出错
        if (items == null || items.isEmpty()) {
            return;
        }
        vp_arl.setOffscreenPageLimit(Math.min(2, items.size()));
        if (layoutStyle >= 1 && layoutStyle < 5) { //1 --4
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) vp_arl.getLayoutParams();
            layoutParams.setMargins(ConvertUtils.dp2px(15), 0, ConvertUtils.dp2px(15), 0);
            vp_arl.setLayoutParams(layoutParams);
            vp_arl.setPageMargin(ConvertUtils.dp2px(5));
            if (layoutStyle == 2 || layoutStyle == 3) {
                vp_arl.sestScaleTransformPage(true);
                if (layoutStyle == 3) {
                    vp_arl.setLeftClick(true);
                }
            }
            mRlLayout.setOnTouchListener(new OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    return vp_arl.dispatchTouchEvent(event);
                }
            });
        }
        if (items.size() > 1) {
            vp_arl.setCurrentItem(items.size() * 120, false);
        }
        //自动滚动
        setAutoRoll(true);
    }

    @Override
    public void setStyle(int style) {
        if (style <= 0) {
            style = 91;
        }
        this.style = style;
        int time = getAttr(0);
        layoutStyle = getAttr(1);
        showDog = getAttr(2) != 1;
        if (time <= 0) {
            stepTime = STEPTIME_DEF;
        } else if (time == 1) {
            stepTime = STEPTIME_1;
        } else if (time == 2) {
            stepTime = STEPTIME_2;
        } else if (time == 3) {
            stepTime = STEPTIME_3;
        } else if (time == 4) {
            stepTime = STEPTIME_4;
        } else if (time == 5) {
            stepTime = STEPTIME_5;
        } else if (time == 6) {
            stepTime = STEPTIME_6;
        } else if (time == 7) {
            stepTime = STEPTIME_7;
        } else if (time == 8) {
            stepTime = STEPTIME_8;
        } else if (time == 9) {
            stepTime = STEPTIME_9;
        } else {
            stepTime = STEPTIME_DEF;
        }

    }

    public void setAutoRoll(boolean autoRoll) {
        this.autoRoll = autoRoll;
        if (!autoRoll) {
            handler.removeCallbacks(showNextPager);
        }
        handler.postDelayed(showNextPager, stepTime);
    }

    public Runnable showNextPager = new Runnable() {

        @Override
        public void run() {
            // 防止重复执行
            handler.removeCallbacks(this);
            // 在进行任务的时候，去判断是否允许，允许才切页面，才定时更新
            if (!autoRoll) {
                return;
            }
            showNextPager();
            handler.postDelayed(this, stepTime);
        }

    };

    /**
     * true 从左向右移动
     */
    private void showNextPager() {
        int currentItem = vp_arl.getCurrentItem();
        if (currentItem == 0) {
            isRight = true;
        } else if (currentItem == adapter.getCount() - 1) {
            isRight = false;
        }
        // 当从左向右移动   角标 +1  ，否则 角标 -1
        int nextIndex = isRight ? currentItem + 1 : currentItem - 1;
        vp_arl.setCurrentItem(nextIndex);
    }

    public ViewPager.OnPageChangeListener PageListener = new ViewPager.OnPageChangeListener() {

        @Override
        public void onPageSelected(int position) {
            // 改变点的状态
            int size = items.size();
            if (size > 1 && showDog) {
                for (int i = 0; i < size; i++) {
                    ll_arl.getChildAt(i).setEnabled(i != position % size);
                }
            }
        }

        @Override
        public void onPageScrolled(int arg0, float arg1, int arg2) {

        }

        @Override
        public void onPageScrollStateChanged(int arg0) {

        }
    };

    private void addDots() {
        if (items == null) {
            return;
        }
        if (items.size() == 1) {
            return;
        }
        for (int i = 0; i < items.size(); i++) {
            // 把10dp 转成对应的像素
            View view = new View(getContext());

            int dotWidth = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 18, getResources()
                            .getDisplayMetrics());

            int dotHight = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 3, getResources()
                            .getDisplayMetrics());

            // 设置宽高、marging
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    dotWidth, dotHight);
            params.setMargins(0, 0, dotHight, 0);
            view.setLayoutParams(params);
            // 指定背景是选择器，在pagechangelistener中只去改变状态，更加面向对象，易于控制
            view.setBackgroundResource(R.drawable.arl_ball_bg_selector02);
            ll_arl.addView(view);
        }

    }

    public class MyPagerAdapter extends RecyclingPagerAdapter {

        @Override
        public int getCount() {
            return items == null ? 0 : items.size() > 1 ? Integer.MAX_VALUE : items.size();
        }

        @Override
        public View getView(int position, View convertView, ViewGroup container) {
            final int index = position % items.size();
            ImageView imageView;
            if (convertView == null) {
                imageView = new ImageView(getContext());
            } else {
                imageView = (ImageView) convertView;
            }
            setImageView(imageView, items.get(index));
            imageView.setTag(R.id.tag_action, items.get(index).action);
            imageView.setTag(R.id.tag_1, position);
            imageView.setTag(R.id.tag_2, index);
            Activity context = (Activity) getContext();
            if (context.getClass().getName().contains("ClinicActivity")) {
                imageView.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_CLINIC_BANNER);
            } else if (context.getClass().getName().contains("MainActivity")) {
                imageView.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_BANNER);
            }
            imageView.setOnClickListener(itemClick);
            return imageView;
        }

    }

    //获取style
    private int getAttr(int index) {
        try {
            return Integer.parseInt(String.valueOf(style).substring(index, index + 1));
        } catch (Throwable e) {
            return 0;
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        setAutoRoll(false);
    }

    @Override
    public void onResume() {
        super.onResume();
        setAutoRoll(true);
    }
}
