package com.ybmmarket20.view

import android.graphics.Rect
import android.util.Log
import android.view.TouchDelegate
import android.view.View

/**
 * 扩大点击区域
 */
fun View.expendTouchArea(expendSize: Int, unLeft: <PERSON>olean = false, unTop: <PERSON>olean = false, unRight: <PERSON>olean = false, unBottom: Boolean = false) {
    val parentView = parent as View
    post {
        val rect = Rect()
        getHitRect(rect)
        Log.i("getHitRect", rect.toString())
        if(!unLeft) rect.left -= expendSize
        if(!unTop) rect.top -= expendSize
        if(!unRight) rect.right += expendSize
        if(!unBottom) rect.bottom += expendSize
        parentView.touchDelegate = TouchDelegate(rect, this)
    }
}