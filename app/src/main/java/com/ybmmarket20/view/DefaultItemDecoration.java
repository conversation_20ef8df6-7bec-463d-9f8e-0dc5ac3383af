package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import androidx.annotation.ColorRes;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;


/**
 * @author: yuhaibo
 * @time: 2018/12/21 10:38.
 * Description: 默认的分隔线
 */
public class DefaultItemDecoration extends RecyclerView.ItemDecoration {
    private Context mContext;
    private View childView;
    private int separatorWidth = 1;
    private int lineColorRes = R.color.divider_line_base_1px;

    private boolean hasHeaderAndFooter = false;

    public DefaultItemDecoration(Context context) {
        mContext = context;
    }

    public DefaultItemDecoration(Context mContext, int separatorWidth) {
        this.mContext = mContext;
        this.separatorWidth = separatorWidth;
    }

    public void setLineColorResId(@ColorRes int resId) {
        lineColorRes = resId;
    }

    public void setHasHeaderAndFooter(boolean hasHeaderAndFooter) {
        this.hasHeaderAndFooter = hasHeaderAndFooter;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        WrapLinearLayoutManager layoutManager = (WrapLinearLayoutManager) parent.getLayoutManager();
        int position = parent.getChildAdapterPosition(view);
        int itemCount = parent.getAdapter().getItemCount();
        if (layoutManager.getOrientation() == WrapLinearLayoutManager.VERTICAL) {//竖直方向的
            if (hasHeaderAndFooter) {
                outRect.top = separatorWidth;
                if (position == itemCount - 1) {
                    outRect.bottom = separatorWidth;
                }
            } else {
                if (parent.getChildAdapterPosition(view) != 0) {
                    outRect.top = separatorWidth;
                }
            }
        } else {
            if (hasHeaderAndFooter) {
                outRect.left = separatorWidth;
                if (position == itemCount - 1) {
                    outRect.right = separatorWidth;
                }
            } else {
                if (parent.getChildAdapterPosition(view) != 0) {
                    outRect.left = separatorWidth;
                }
            }

        }
        childView = view;
    }

    @Override
    public void onDraw(Canvas canvas, RecyclerView parent, RecyclerView.State state) {
        Paint mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(ContextCompat.getColor(mContext, lineColorRes));

        boolean isVertical = true;
        if (parent.getLayoutManager() instanceof WrapLinearLayoutManager) {
            isVertical = ((WrapLinearLayoutManager) parent.getLayoutManager()).getOrientation() == WrapLinearLayoutManager.VERTICAL;
        }
        int childCount = parent.getChildCount();

        Rect rect = new Rect();
        if (childCount > 1) {
            for (int i = 0; i < childCount; i++) {
                if (i == 0 && !hasHeaderAndFooter) {
                    continue;
                }
                View view = parent.getChildAt(i);
                if (isVertical) {//竖直方向的
                    if (childView != null) {
                        rect.left = childView.getPaddingLeft();
                        rect.right = parent.getWidth() - childView.getPaddingRight();
                    }
                    rect.top = view.getTop() - separatorWidth;
                    rect.bottom = view.getTop();
                    if (hasHeaderAndFooter && i == childCount - 1) {
                        //最后一项加footer
                        canvas.drawRect(rect.left, view.getBottom(), rect.right, view.getBottom() + separatorWidth, mPaint);
                    }
                } else {
                    if (childView != null) {
                        rect.top = childView.getTop();
                        rect.bottom = parent.getHeight() - childView.getPaddingBottom();
                    }
                    rect.left = view.getLeft() - separatorWidth;
                    rect.right = view.getLeft();
                    if (hasHeaderAndFooter && i == childCount - 1) {
                        //最后一项加footer
                        canvas.drawRect(view.getRight(), rect.top, view.getRight() + separatorWidth, rect.bottom, mPaint);
                    }
                }
                canvas.drawRect(rect, mPaint);
            }
        }
    }
}
