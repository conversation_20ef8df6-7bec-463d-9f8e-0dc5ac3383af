package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import androidx.annotation.Px;
import androidx.core.view.MotionEventCompat;

import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;

import com.apkfuns.logutils.LogUtils;
import com.tencent.smtt.export.external.interfaces.ConsoleMessage;
import com.tencent.smtt.export.external.interfaces.PermissionRequest;
import com.tencent.smtt.export.external.interfaces.SslError;
import com.tencent.smtt.export.external.interfaces.SslErrorHandler;
import com.tencent.smtt.export.external.interfaces.WebResourceError;
import com.tencent.smtt.export.external.interfaces.WebResourceRequest;
import com.tencent.smtt.export.external.interfaces.WebResourceResponse;
import com.tencent.smtt.sdk.ValueCallback;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;
import com.ybm.app.common.SmartExecutorManager;
import com.ybmmarket20.activity.CommonH5Activity;
import com.ybmmarket20.bean.Hybrid;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.xyyreport.XyyReportManager;
import com.ybmmarket20.xyyreport.session.SessionManager;
import com.ybmmarket20.xyyreport.spm.XyyReportActivity;

import java.util.HashMap;

/**
 * h5webview
 */
public class X5WebView extends WebView {
    private WebViewProgressBar progressBar;
    private WebView wbH5;
    private boolean nestedScroll = false;
    private boolean isCache = false;
    public static final String CACHE_NO = "0";//不使用
    public static final String CACHE_NATIVE = "1";//使用native 保存文件
    public static final String CACHE_WEBVIEW = "2";//使用webview 自己的缓存文件
    public static final String CACHE_NET = "3";//当有网络时不使用缓存
    private WebViewListener webViewListener;
    private FileChooserListener fileChooserListener;

    private GFBTCallBack mGFBTCallBack;
    protected Hybrid hybrid;

    public String share_type;
    public String share_desc_pyq;
    public String share_desc;
    public String share_url;
    public String share_title;

    private String jgspid;


    public X5WebView(Context context) {
        super(context);
        init(context, null);
    }

    public X5WebView(Context context, String jgspid) {
        super(context);
        this.jgspid = jgspid;
        init(context, null);
    }

    public X5WebView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        int style = -1;
        if (attrs != null) {
            style = attrs.getAttributeIntValue("http://schemas.android.com/apk/res/android", "style", -1);
        }
        if (style != 100) {
            progressBar = new WebViewProgressBar(context);
            progressBar.setLayoutParams(new ViewGroup.LayoutParams
                    (ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            progressBar.setVisibility(INVISIBLE);
            addView(progressBar);
        }
        wbH5 = this;
        initWebView();
    }

    private void initWebView() {
        wbH5.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onReceivedTitle(WebView webView, String s) {
                super.onReceivedTitle(webView, s);
                if (webViewListener != null) {
                    webViewListener.onReceivedTitle(webView, s);
                }
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                if (progressBar == null) {
                    return;
                }
                if (newProgress == 100) {
                    progressBar.setProgress(100);
                    SmartExecutorManager.getInstance().executeUI(new Runnable() {
                        @Override
                        public void run() {
                            if (progressBar == null) {
                                return;
                            }
                            progressBar.setVisibility(View.INVISIBLE);
                        }
                    }, 200);
                } else if (progressBar.getVisibility() == View.INVISIBLE) {
                    progressBar.setVisibility(View.VISIBLE);
                }
                if (newProgress < 5) {
                    newProgress = 5;
                }
                progressBar.setProgress(newProgress);
                super.onProgressChanged(view, newProgress);
            }

            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                if (consoleMessage != null && ConsoleMessage.MessageLevel.ERROR.equals(consoleMessage.messageLevel())) {
                    // BugUtil.sendBug(new NullPointerException("" + consoleMessage.message() + " :source " + consoleMessage.sourceId() + " :line " + consoleMessage.lineNumber()));
                    return true;
                }
                return super.onConsoleMessage(consoleMessage);
            }

            //4.1以上4.4以下的系统web选择文件
            @Override
            public void openFileChooser(ValueCallback<Uri> uploadFile, String acceptType, String capture) {
                if (fileChooserListener != null) {
                    fileChooserListener.onFileChooserApiLow(uploadFile, acceptType, capture);
                } else {
                    super.openFileChooser(uploadFile, acceptType, capture);
                }
            }

            @Override
            public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> valueCallback, FileChooserParams fileChooserParams) {
                if (fileChooserListener != null) {
                    fileChooserListener.onFileChooserApiHei(valueCallback, fileChooserParams);
                    return true;
                }
                return super.onShowFileChooser(webView, valueCallback, fileChooserParams);
            }

            @Override
            public void onPermissionRequest(PermissionRequest permissionRequest) {
                //先申请权限，没有权限会闪退。
                fileChooserListener.onRequestPermission(permissionRequest);
            }
        });
        wbH5.setWebViewClient(new WebViewClient() {

            @Override
            public void onPageStarted(WebView webView, String url, Bitmap bitmap) {
                super.onPageStarted(webView, url, bitmap);
                // 处理广发白条的回调
                if (mGFBTCallBack != null) {
                    mGFBTCallBack.handleResult(url);
                }
            }

            @Override
            public void onReceivedSslError(WebView webView, SslErrorHandler sslErrorHandler, SslError sslError) {
                sslErrorHandler.proceed();  //接受所有证书
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                if (webViewListener != null) {
                    webViewListener.onReceivedError(view, errorCode, description, failingUrl);
                }
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url == null) {
                    return false;
                }
                if (RoutersUtils.appScheme(url)) {
                    LogUtils.d("url:" + url);
                    if (url.startsWith("ybmpage://paywayfordrugschoolactivity")) {
                        RoutersUtils.openForResult(url, CommonH5Activity.H5_REQUESTCODE);
                        return true;
                    }
                    RoutersUtils.open(url);
                    return true;
                } else if (url.startsWith("http") || url.startsWith("Http")) {
                    return super.shouldOverrideUrlLoading(view, url);
                } else if (url.startsWith("tel:") || url.startsWith("sms:") || url.startsWith("qq:")) {//sms tel qq
                    if (url.startsWith("tel:")) {
                        RoutersUtils.open("ybmaction://tel?num=" + url.substring(4) + "&show=1");
                        return true;
                    } else if (url.startsWith("sms:")) {
                        RoutersUtils.open("ybmaction://sms?num=" + url.substring(4));
                        return true;
                    } else if (url.startsWith("qq:")) {
                        RoutersUtils.open("ybmaction://qq?num=" + url.substring(4));
                        return true;
                    } else {
                        return super.shouldOverrideUrlLoading(view, url);
                    }
                } else {
                    return super.shouldOverrideUrlLoading(view, url);
                }

            }

            //资源重定向
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                return super.shouldInterceptRequest(view, request);
            }

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
                return super.shouldInterceptRequest(view, url);
            }

            @Override
            public void onPageFinished(WebView webView, String url) {
                super.onPageFinished(webView, url);
            }

            @Override
            public void onReceivedError(WebView webView, WebResourceRequest webResourceRequest, WebResourceError webResourceError) {
                super.onReceivedError(webView, webResourceRequest, webResourceError);
            }

            @Override
            public void onReceivedHttpError(WebView webView, WebResourceRequest webResourceRequest, WebResourceResponse webResourceResponse) {
                super.onReceivedHttpError(webView, webResourceRequest, webResourceResponse);
            }
        });

        wbH5.setHorizontalScrollbarOverlay(false);
        wbH5.setScrollBarStyle(View.SCROLLBARS_OUTSIDE_OVERLAY);
        wbH5.setVerticalScrollbarOverlay(false);
        wbH5.setHorizontalScrollBarEnabled(false);
        wbH5.setScrollbarFadingEnabled(false);
        WebSettings webSetting = wbH5.getSettings();
        webSetting.setSavePassword(false);
        webSetting.setAllowFileAccess(true);
        webSetting.setAllowContentAccess(true);
        webSetting.setAllowFileAccessFromFileURLs(true);
        webSetting.setAllowUniversalAccessFromFileURLs(true);
        webSetting.setSupportZoom(false);
        webSetting.setBuiltInZoomControls(false);
        webSetting.setUseWideViewPort(true);
        webSetting.setSupportMultipleWindows(false);
        webSetting.setLoadWithOverviewMode(true);
        webSetting.setAppCacheEnabled(true);
        webSetting.setDatabaseEnabled(true);
        webSetting.setDomStorageEnabled(true);
        webSetting.setJavaScriptEnabled(true);
        webSetting.setGeolocationEnabled(true);
        webSetting.setMixedContentMode(WebSettings.LOAD_NORMAL);
        webSetting.setAppCacheMaxSize(500 * 1024 * 1024);
        if (getContext() instanceof BaseActivity) {
            hybrid = new Hybrid((BaseActivity) getContext());
        } else {
            hybrid = new Hybrid(null);
        }
        hybrid.setCallback(webViewCallback);
        hybrid.setEnvironmentCallback(environmentCallback);
        hybrid.setJgspid(jgspid);
//        hybrid.setSpmParamsCallback(spmParamsCallback);
        wbH5.addJavascriptInterface(hybrid, "hybrid");
    }

    public void setCacheMode(String cache) {
        WebSettings webSetting = wbH5.getSettings();
        if (TextUtils.isEmpty(cache) || cache.equals("null") || cache.equals("NULL")) {
            cache = CACHE_WEBVIEW;
        }
        switch (cache) {
            case CACHE_NATIVE://原生缓存，webview禁止缓存
                isCache = true;
                webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
                break;
            case CACHE_NET:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
                break;
            case CACHE_NO:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
                break;
            case CACHE_WEBVIEW:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_DEFAULT);
                break;
            default:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
                break;
        }
    }

    //设置监听
    public void setWebViewListener(WebViewListener webViewListener) {
        this.webViewListener = webViewListener;
        if (hybrid != null) {
            hybrid.setActivityCallback(webViewListener);
        }
    }

    public void setBindBankCardCallback(Hybrid.InjectJDPayCallback injectJDPayCallback) {
        if (hybrid != null) {
            hybrid.setBindBankCardCallback(injectJDPayCallback);
        }
    }

    public WebViewListener getWebViewListener() {
        return webViewListener;
    }

    public void setFileChooserListener(FileChooserListener listener) {
        fileChooserListener = listener;
    }

    public void setGFBTCallBack(GFBTCallBack callBack) {
        mGFBTCallBack = callBack;
    }

    public interface GFBTCallBack {
        void handleResult(String url);
    }

    public WebView getWbH5() {
        return wbH5;
    }

    public interface WebViewListener extends Hybrid.InjectActivityCallback {
        void onReceivedTitle(WebView webView, String s);

        void setStatusColor(WebView webView, int color);

        void onReceivedError(WebView view, int errorCode, String description, String failingUrl);

        void onScrollChanged(int x, int y, int oldx, int oldy);
    }

    public interface FileChooserListener {
        void onFileChooserApiLow(ValueCallback<Uri> uploadFile, String acceptType, String capture);

        void onFileChooserApiHei(ValueCallback<Uri[]> uploadFile, WebChromeClient.FileChooserParams fileChooserParams);

        void onRequestPermission(PermissionRequest permissionRequest);
    }



    protected int getStrColor(String color) {
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && back()) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    //返回历史记录
    public boolean back() {
        if (wbH5 != null && wbH5.getVisibility() == View.VISIBLE && wbH5.canGoBack()) {
            wbH5.goBack();
            return true;
        }
        return false;
    }

    @Override
    public void loadUrl(String url) {
        if (url != null && (url.startsWith("javascript") || url.endsWith("html") || url.endsWith("htm"))) {
            super.loadUrl(url);
        } else {
            if(url != null && url.contains("seckillactivity")){
                String value = System.currentTimeMillis()
                        + "_" + SpUtil.getMerchantid()
                        + "_" + (long)((Math.random()*9+1)*100000);

                url = url + (url.contains("?")? "&requestId=":"?requestId=") + value;

                HashMap<String, String> params = new HashMap<>();
                params.put("eventRequestId", value);
                XyyIoUtil.track("c2c_Seckill_More_Click", params);
            }
            loadUrlAddMerchant(url);
        }
    }

    public void loadUrlAddMerchant(String url) {
        if (url != null && hybrid != null) {
            if (TextUtils.isEmpty(RoutersUtils.getParameter(url, "merchantId"))) {
                if (url.contains("?")) {
                    url += "&merchantId=" + SpUtil.getMerchantid();
                } else {
                    url += "?merchantId=" + SpUtil.getMerchantid();
                }
            }
        }
        super.loadUrl(url);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (webViewListener != null) {
            webViewListener.onScrollChanged(l, t, oldl, oldl);
        }
    }

    public void setNestedScroll(boolean nestedScroll) {
        this.nestedScroll = nestedScroll;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            wbH5.setNestedScrollingEnabled(nestedScroll);
        }
    }

    @Override
    public void scrollTo(@Px int x, @Px int y) {
        if (nestedScroll) {
            super.scrollTo(x, y);
        } else {
            super.scrollTo(0, 0);
        }
    }

    public void callJs(String methodName, String... param) {
        if (hybrid != null) {
            hybrid.callJs(methodName, param);
        }
    }

    private Hybrid.IEnvironmentCallback environmentCallback = new Hybrid.IEnvironmentCallback() {
        @Override
        public void onCallback(String environmentType) {
//            callJs("getEnvironmentCallback", environmentType);
            wbH5.post(new Runnable() {
                @Override
                public void run() {
                    wbH5.loadUrl("javascript:getEnvironmentCallback('" + environmentType + "')");
                }
            });
        }
    };

//    private Hybrid.ISpmParamsCallback spmParamsCallback = () -> {
//        Map<String, String> map = new HashMap<>();
//        String session = SessionManager.get().getSession();
//        map.put("session", session);
//        if (getContext() instanceof XyyReportActivity) {
//            XyyReportActivity xyyReportActivity = (XyyReportActivity)getContext();
//            String spmUrl = xyyReportActivity.getSpmUrl();
//            String scmUrl = xyyReportActivity.getScmUrl();
//            String spmPre = xyyReportActivity.getSpmPre();
//            String scmPre = xyyReportActivity.getScmPre();
//            if (spmUrl != null) {
//                map.put("spm_url", spmUrl);
//            }
//            if (spmUrl != null) {
//                map.put("scm_url", scmUrl);
//            }
//            if (spmPre != null) {
//                map.put("spm_pre", spmPre);
//            }
//            if (scmPre != null) {
//                map.put("scm_pre", scmPre);
//            }
//        }
//        Gson gson = new Gson();
//        return gson.toJson(map);
//    };

    //给桥接的bean对象hybird设置回掉
    private Hybrid.InjectWebViewCallback webViewCallback = new Hybrid.InjectWebViewCallback() {
        @Override
        public Context getWebViewContext() {
            return wbH5 != null ? wbH5.getContext() : null;
        }

        @Override
        public void webViewScroll(boolean scrollable) {
            setNestedScroll(scrollable);
        }

        @Override
        public void showSoftInput() {
            try {
                InputMethodManager inputManager = (InputMethodManager) wbH5.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                inputManager.showSoftInput(wbH5, InputMethodManager.SHOW_IMPLICIT);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        @Override
        public void hideSoftInput() {
            try {
                InputMethodManager inputManager = (InputMethodManager) wbH5.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                inputManager.hideSoftInputFromWindow(wbH5.getWindowToken(), 0);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        @Override
        public void openHardware(boolean open) {
            if (!open) {
                wbH5.setLayerType(LAYER_TYPE_SOFTWARE, null);
            } else {
                wbH5.setLayerType(LAYER_TYPE_HARDWARE, null);
            }
        }

        @Override
        public void scrollChange(int x, int y, int oldX, int oldY) {
            if (webViewListener != null) {
                webViewListener.onScrollChanged(x, y, oldX, oldY);
            }
        }

        @Override
        public void loadJsUrl(final String jsUrl) {
            wbH5.post(new Runnable() {
                @Override
                public void run() {
                    wbH5.loadUrl(jsUrl);
                }
            });
        }

    };

    @Override
    public boolean canScrollHorizontally(int direction) {

        return super.canScrollHorizontally(direction);
    }

    private boolean isScrollX = false;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (MotionEventCompat.getPointerCount(event) == 1) {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    isScrollX = false;
                    //事件由webview处理
                    getParent().getParent()
                            .requestDisallowInterceptTouchEvent(true);
                    break;
                case MotionEvent.ACTION_MOVE:
                    //嵌套Viewpager时
                    getParent().getParent()
                            .requestDisallowInterceptTouchEvent(!isScrollX);
                    break;
                default:
                    getParent().getParent()
                            .requestDisallowInterceptTouchEvent(false);
            }
        } else {
            //使webview可以双指缩放（前提是webview必须开启缩放功能，并且加载的网页也支持缩放）
            getParent().getParent().
                    requestDisallowInterceptTouchEvent(true);
        }
        return super.onTouchEvent(event);
    }
}