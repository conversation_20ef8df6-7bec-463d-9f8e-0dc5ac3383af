package com.ybmmarket20.view

import android.content.Context
import android.graphics.Typeface
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.GlideDrawable
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.utils.tagStyle

/**
 * <AUTHOR> Brin
 * @date : 2020/12/2 - 14:33
 * @Description :
 * @version
 */
class ShopNameWithTagView : FlexboxLayout {
    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        initView()
    }


    private fun initView() {
//        orientation = HORIZONTAL
//        gravity = Gravity.CENTER_VERTICAL or Gravity.LEFT
        flexWrap = FlexWrap.WRAP
    }

    /**
     * @param list          标签列表
     * @param contentStr    标签后的内容名称
     * @param maxTagCount   标签的最大显示数量限制
     *
     */
    fun bindData(list: List<TagBean?>?, contentStr: String? = null, maxTagCount: Int = 2) {

        if (childCount > 0) {
            removeAllViews()
        }
        val size = Math.min(maxTagCount, list?.size ?: 0)
        childrenWith = 0f


        for (a in 0 until size) {
            val createTagView = createTagView(list?.get(a))
            if (createTagView == null) {
                break
            } else {
                addView(createTagView)
            }
        }
        contentStr?.let {
            addView(createContentView(contentStr))
        }


    }

    fun bindData(list: List<TagBean?>?, contentStr: String? = null) {
        bindData(list, contentStr, maxTagCount = 5)
    }

    var childrenWith: Float = 0f


    override fun addView(child: View?) {
        super.addView(child)

    }

    private fun createTagView(bean: TagBean?): TextView? {
        val textView = TextView(context)
        val params = LayoutParams(LayoutParams.WRAP_CONTENT, ConvertUtils.dp2px(15f))

        params.setMargins(0, 0, ConvertUtils.dp2px(3f), 0)
        textView.gravity = Gravity.CENTER
        textView.isSingleLine = true
        textView.ellipsize = TextUtils.TruncateAt.END
        textView.layoutParams = params
        textView.setPadding(ConvertUtils.dp2px(3f), 2, ConvertUtils.dp2px(3f), 2)
        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)
        textView.tagStyle(bean)
        if (!TextUtils.isEmpty(bean?.appIcon)) {
            Glide.with(context)
                .load(AppNetConfig.getCDNHost() + bean?.appIcon) // bean.imageUrl 是网络图片的 URL
                .into(object : SimpleTarget<GlideDrawable>() {
                    override fun onResourceReady(
                        resource: GlideDrawable?,
                        glideAnimation: GlideAnimation<in GlideDrawable>?
                    ) {
                        resource?.setBounds(0, 0, ConvertUtils.dp2px(7f), ConvertUtils.dp2px(10f))
                        // 设置 drawableStart
                        textView.setCompoundDrawablesRelative(
                            resource,
                            null,
                            null,
                            null
                        )
                    }
                })
        }
        // LogUtils.tag("tagview").d("textviewwith = ${bean?.text}${textView.paint.measureText(bean?.text) + textView.paddingLeft + textView.paddingRight + ConvertUtils.dp2px(3f)}")
        return textView
    }

    private fun createContentView(contentStr: String?): TextView {
        val textView = TextView(context)
        val params = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        textView.gravity = Gravity.CENTER
        textView.isSingleLine = true
        textView.ellipsize = TextUtils.TruncateAt.END
        textView.layoutParams = params
        textView.typeface = Typeface.DEFAULT_BOLD
        textView.setTextColor(UiUtils.getColor(R.color.color_292933))
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f)
        textView.text = contentStr ?: ""
        return textView
    }
}