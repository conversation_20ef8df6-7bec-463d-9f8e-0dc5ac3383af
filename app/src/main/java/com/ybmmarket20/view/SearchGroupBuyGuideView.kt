package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.widget.FrameLayout
import com.ybmmarket20.R

/**
 * 搜索页面组合购引导
 */
class SearchGroupBuyGuideView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 引导点击事件监听器，用于处理用户点击后的业务逻辑
    private var onGuideClickListener: OnGuideClickListener? = null

    /**
     * 引导点击监听器接口
     * 当用户点击引导组件（"我知道了"按钮或蒙版背景）时触发
     */
    interface OnGuideClickListener {
        /**
         * 引导被点击时的回调方法
         * 在此方法中应该：
         * 1. 调用SearchGuideManager.markDeliveryGuideShown()记录显示状态
         * 2. 进行相关的埋点统计
         * 3. 执行其他业务逻辑
         */
        fun onGuideClick()
    }

    init {
        initView()
    }

    private fun initView() {
        // 加载布局文件
        LayoutInflater.from(context).inflate(R.layout.view_search_group_buy_guide, this, true)

        // 确保View可以接收触摸事件
        isClickable = true
        isFocusable = true

        // 点击整个view的任何地方都关闭蒙层
        this.setOnClickListener {
            hideGuide()                          // 隐藏引导蒙版
            onGuideClickListener?.onGuideClick() // 通知外部监听器，用于记录显示状态和埋点
        }
        // 默认隐藏
        visibility = GONE
    }

    /**
     * 显示引导，相对于指定的SearchDynamicLabelView定位
     */
    fun showGuide() {
        visibility = VISIBLE
    }

    /**
     * 隐藏引导
     */
    fun hideGuide() {
        visibility = GONE
    }

    /**
     * 设置引导点击监听器
     */
    fun setOnGuideClickListener(listener: OnGuideClickListener?) {
        this.onGuideClickListener = listener
    }

    /**
     * 检查是否正在显示
     */
    fun isShowing(): Boolean {
        return visibility == VISIBLE
    }

    /**
     * 重写触摸事件处理，确保点击事件能被正确处理
     */
    override fun onTouchEvent(event: MotionEvent?): Boolean {

        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                return true // 消费DOWN事件
            }
            MotionEvent.ACTION_UP -> {
                hideGuide()
                onGuideClickListener?.onGuideClick()
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    /**
     * 重写拦截触摸事件，确保事件能传递到当前View
     */
    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        return true // 拦截所有触摸事件，让当前View处理
    }
}
