package com.ybmmarket20.view;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;

import com.ybm.app.utils.UiUtils;
import com.ybmmarket20.R;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/6/8.
 * e-mail:<EMAIL>
 */

public abstract class TransparentDialog extends Dialog {

    public static class ItemInfo {

        public String itemName;

        public int id;

        public Object extraInfo;

    }

    public ItemSelectListener listener;

    public interface ItemSelectListener {

        void itemSelect(ItemInfo itemInfo);
    }


    public void setItemSelectListener(ItemSelectListener listener) {
        this.listener = listener;

    }

    public TransparentDialog(@NonNull Context context) {
        super(context, R.style.Dialog_Empty);
        setContentView(getLayoutId());
        setCancelable(true);
        if (!centerInWindow()) {
            Window window = this.getWindow();
            WindowManager.LayoutParams attributes = window.getAttributes();
            window.setGravity(Gravity.LEFT | Gravity.BOTTOM);
            window.getDecorView().setPadding(0, 0, 0, 0);
            window.setWindowAnimations(R.style.dialogWindowAnim);
            attributes.width = UiUtils.getScreenWidth();
            window.setAttributes(attributes);
        }


    }


    public abstract int getLayoutId();

    @Override
    public void dismiss() {
        if (isShowing()) {
            super.dismiss();
        }
    }


    public boolean centerInWindow() {

        return false;
    }
}
