package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Rect;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ClinicCategory;
import com.ybmmarket20.bean.ClinicTypeViewItem;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsListBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 诊所其它页面布局
 */
public class ClinicLayout extends CommonRecyclerView {
    private final GoodsListAdapter adapter;
    private int type;//分类
    private ClinicTypeViewItem typeViewItem;
    private int page = 1;
    private final static int PAGE_SIZE = 10;
    private final ClinicHeaderLayout headerLayout;
    private int selPostion;

    public ClinicLayout(Context context, final int type) {
        super(context);
        this.type = type;
        adapter = new GoodsListAdapter(R.layout.item_goods, new ArrayList<RowsBean>(), false, false);
        adapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {
                RoutersUtils.open("ybmpage://productdetail/" + rows.getId());
            }
        });
        setAdapter(adapter, new WrapLinearLayoutManager(getContext()));
        adapter.openLoadMore(PAGE_SIZE, true);
        headerLayout = new ClinicHeaderLayout(context);
        headerLayout.setItemClick(new ClinicHeaderLayout.ItemClick() {
            @Override
            public void onItemClick(int position, ClinicTypeViewItem bean) {
                selPostion = position;
                if (typeViewItem != null && typeViewItem.apiType.equals(bean.apiType)) {
                    return;
                }
                typeViewItem = bean;
                getProductData(true);
            }
        });
        adapter.addHeaderView(headerLayout);
        setListener(new Listener() {
            @Override
            public void onRefresh() {
                refresh(true);
            }

            @Override
            public void onLoadMore() {
                getProductData(false);
            }
        });
    }

    private void getProductData(final boolean refresh) {
        final RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOST + "layout/initExhibitionModulePage").addParam("sort", "1").addParam("exhibitionId", typeViewItem.apiType).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        params.put("limit", PAGE_SIZE + "");
        if (!refresh) {
            params.put("offset", page + "");
        }
        HttpManager.getInstance().post(params, new BaseResponse<RowsListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RowsListBean> obj, RowsListBean rowsBeans) {

                if (obj != null && obj.isSuccess() && rowsBeans != null ) {
                    List<RowsBean> rows = rowsBeans.rows;
                    if (rows == null) {
                        rows = new ArrayList<>();
                    }
                    if (rows.size() <= 0) {
                        adapter.notifyDataChangedAfterLoadMore(false);
                        return;
                    }
                    if (refresh) {
                        page = 1;
                        if (rows.size() >= PAGE_SIZE) {
                            adapter.setNewData(rows);
                        } else {
                            adapter.setNewData(rows);
                            adapter.notifyDataChangedAfterLoadMore(false);
                        }
                    } else {//加载更多
                        for (RowsBean bean : rows) {
                            adapter.getData().remove(bean);
                        }
                        if (rows.size() >= PAGE_SIZE) {
                            if (page <= 0) {
                                page = 1;
                            }
                            page++;
                        }
                        adapter.getData().addAll(rows);
                        adapter.notifyDataChangedAfterLoadMore(rows.size() >= PAGE_SIZE);
                    }
                }
            }

        });
    }

    private void refresh(boolean show) {
        setRefreshing(show);
        RequestParams params = RequestParams.newBuilder().addParam("categoryType", type + "")
                .addParam("merchantId", HttpManager.getInstance().getMerchant_id())
                .url(AppNetConfig.CLINIC_CATEGORY_API).build();
        HttpManager.getInstance().post(params, new BaseResponse<ClinicCategory>() {
            @Override
            public void onSuccess(String content, BaseBean<ClinicCategory> obj, ClinicCategory bean) {
                if (bean != null && obj != null) {
                    headerLayout.setData(bean.category);
                    setRefreshing(false);
                    if (bean.category != null && bean.category.size() > 0) {
                        if (selPostion > bean.category.size()) {
                            selPostion = 0;
                        }
                        typeViewItem = bean.category.get(selPostion);
                    } else {
                        typeViewItem = new ClinicTypeViewItem();
                        typeViewItem.apiType = bean.defUrl;
                    }
                    getProductData(true);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                setRefreshing(false);
            }
        });
    }

    public class MyItemDecoration extends RecyclerView.ItemDecoration {
        private int space = ConvertUtils.dp2px(1);
        private int row = 2;

        public MyItemDecoration() {

        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            if (row == 2) {
                outRect.left = space * 2;
                outRect.right = space * 2;
                outRect.bottom = space * 4;
                outRect.top = 0;
            }
        }
    }

    public void selPostion(int index) {
        selPostion = index;
        if (headerLayout != null) {
            headerLayout.selPostion(selPostion);
        }
        refresh(false);
    }

}
