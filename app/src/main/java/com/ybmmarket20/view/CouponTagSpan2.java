package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.style.ImageSpan;

import com.ybmmarket20.R;

public class CouponTagSpan2 extends ImageSpan {
    private int spanWidth=0;
    private int textColor= R.color.white;
    private String tagText="";
    private Drawable drawable;
    private Context context;
    public CouponTagSpan2(@NonNull Drawable drawable, Context context,String tagText,int textColor) {
        super(drawable);
        this.textColor=textColor;
        this.tagText=tagText;
        this.drawable=drawable;
        this.context=context;
    }

    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, @Nullable Paint.FontMetricsInt fm) {
        Rect rect=drawable.getBounds();
        if (fm != null) {
            Paint.FontMetricsInt fmPaint=paint.getFontMetricsInt();
            int fontHeight = fmPaint.bottom - fmPaint.top;
            int drHeight=rect.bottom-rect.top;

            int top= drHeight/2 - fontHeight/4;
            int bottom=drHeight/2 + fontHeight/4;

            fm.ascent=-bottom;
            fm.top=-bottom;
            fm.bottom=top;
            fm.descent=top;
        }
        spanWidth = rect.right;
        return rect.right;
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
        canvas.save();
        int transY = ((bottom-top) - drawable.getBounds().bottom)/2+top;
        canvas.translate(x, transY);
        drawable.draw(canvas);
        canvas.restore();
        paint.setColor(context.getResources().getColor(textColor));
        float textWidth = paint.measureText(tagText);
        canvas.drawText(tagText, (spanWidth - textWidth) / 2, y, paint);
    }
}
