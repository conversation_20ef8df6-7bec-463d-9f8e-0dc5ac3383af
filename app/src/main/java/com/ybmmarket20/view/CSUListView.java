package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.widget.LinearLayout;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.CSUDetailBean;
import com.ybmmarket20.bean.TagBean;
import com.ybmmarket20.view.jdaddressselector.BottomDialog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CSUListView extends LinearLayout {
    private RecyclerView mListView;
    private CSUListAdapter mAdapter;
    private CSUListAdapter.CSUItemEventListener mCsuItemEventListener;

    private boolean mIsMainProductVirtualSupplier;

    public CSUListView(Context context) {
        super(context);

    }

    public CSUListView(Context context, @Nullable @org.jetbrains.annotations.Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public void setListData(List<CSUDetailBean> csuList, TagBean bean){
        setListData(csuList, bean, false);
    }

    public void setListData(List<CSUDetailBean> csuList, TagBean bean, boolean isGray){
        initSubControls(isGray);
        if (bean != null) {
            bean.initCsuList();
        }
        if (csuList != null && csuList.size() > 5) {
            List<CSUDetailBean> showCsuList = new ArrayList<>();
            for (int i = 0; i < 5; i++) {
                showCsuList.add(csuList.get(i));
            }
            CSUDetailBean moreItem = new CSUDetailBean();
            showCsuList.add(moreItem);
            this.mAdapter.setNewData(showCsuList);
            this.mAdapter.setOnMoreItemListener(() -> {
                GiftPoolDialog giftPool = new GiftPoolDialog(getContext());
                giftPool.setData(bean, csuList);
                giftPool.show();
            });
            return;
        }
        this.mAdapter.setNewData(csuList);
    }

    public void setItemEventListener(CSUListAdapter.CSUItemEventListener l){
        mCsuItemEventListener = l;
        if (mAdapter == null) return;
        this.mAdapter.setItemEventListener(l);
    }

    public void setIsMainProductVirtualSupplier(boolean isMainProductVirtualSupplier) {
        this.mIsMainProductVirtualSupplier = isMainProductVirtualSupplier;
        if (mAdapter == null) return;
        mAdapter.setIsMainProductVirtualSupplier(mIsMainProductVirtualSupplier);
    }

    private void initSubControls(boolean isGray){
        mListView = new RecyclerView(getContext());
        LinearLayout.LayoutParams params = new LayoutParams(LayoutParams.MATCH_PARENT, TagGroup.LayoutParams.MATCH_PARENT);
        this.addView(mListView, params);

        if (isGray) {
            mAdapter = new CSUListAdapter(R.layout.item_csu_list_with_gray);
        } else {
            mAdapter = new CSUListAdapter(R.layout.item_csu_list);
        }

        mListView.setAdapter(mAdapter);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        mListView.setLayoutManager(layoutManager);
        if (mIsMainProductVirtualSupplier) {
            mAdapter.setIsMainProductVirtualSupplier(mIsMainProductVirtualSupplier);
        }
        if (mCsuItemEventListener != null) {
            this.mAdapter.setItemEventListener(mCsuItemEventListener);
        }
    }

}
