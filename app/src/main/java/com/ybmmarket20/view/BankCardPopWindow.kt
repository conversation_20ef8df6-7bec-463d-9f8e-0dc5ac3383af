package com.ybmmarket20.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybmmarket20.R
import com.ybmmarket20.bean.PayTypeCommon
import com.ybmmarket20.bean.PayTypeEntry
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.PAY_LAYOUT_TYPE_BANK_CARD
import com.ybmmarket20.viewmodel.PAY_LAYOUT_TYPE_BANK_CARD_ADD
import com.ybmmarket20.viewmodel.PayWayV2ViewModel


/**
 * <AUTHOR> Brin
 * @date : 2020/10/27 - 17:34
 * @Description :
 * @version
 */
class BankCardPopWindow : BaseBottomPopWindow {

    lateinit var rvSelectBankCard: RecyclerView
    lateinit var ivClose: ImageView
    lateinit var mAdapter: DiscountAdapter
    var mData: List<PayTypeEntry>
    var payWayV2ViewModel: PayWayV2ViewModel
    var mContext: Context

    var popWindowSelectCardCallback: ((id: String) -> Unit)? = null


    constructor(mContext: Context, data: List<PayTypeEntry>, payWayV2ViewModel: PayWayV2ViewModel) : super() {
        this.mContext = mContext
        mData = data
        this.payWayV2ViewModel = payWayV2ViewModel
        initDadaAndView()
    }

    private fun initDadaAndView() {
        rvSelectBankCard = getView(R.id.rvSelectBankCard)
        ivClose = getView(R.id.iv_close)
        rvSelectBankCard.layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
        mAdapter = DiscountAdapter(mData)
        rvSelectBankCard.adapter = mAdapter
        ivClose.setOnClickListener {
            dismiss()
        }
    }

    override fun getLayoutParams(): LinearLayout.LayoutParams {
        return LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    override fun getLayoutId(): Int {
        return R.layout.select_bank_card_pop
    }

    override fun initView() {}

    inner class DiscountAdapter :
        YBMBaseMultiItemAdapter<PayTypeEntry> {

        constructor(data: List<PayTypeEntry>) : super(data) {
            //添加银行卡
            addItemType(PAY_LAYOUT_TYPE_BANK_CARD_ADD, R.layout.item_pay_way_add_bank_card_popup)
            //银行卡
            addItemType(PAY_LAYOUT_TYPE_BANK_CARD, R.layout.item_pay_way_bank_card_popup)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: PayTypeEntry) {
            whenAllNotNull(baseViewHolder, t) {holder, bean ->
                when (bean.itemType) {
                    //添加银行卡
                    PAY_LAYOUT_TYPE_BANK_CARD_ADD -> binAddBankCardItemView(holder, bean)
                    //银行卡
                    PAY_LAYOUT_TYPE_BANK_CARD -> bindBankCardItemView(holder, bean)
                }
            }
        }
        //添加银行卡
        private fun binAddBankCardItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
            holder.itemView.setOnClickListener {
//                payWayV2ViewModel.showLoading()
//                payWayV2ViewModel.queryPWSettingStatus()
                payWayV2ViewModel.jumpToAddBankCard()
                dismiss()
            }
        }
        //银行卡
        private fun bindBankCardItemView(holder: YBMBaseHolder, bean: PayTypeEntry) {
            val payBean = bean as PayTypeCommon
            val logoView = holder.getView<ImageView>(R.id.ivLogo)
            val tvBankName = holder.getView<TextView>(R.id.tvBankName)
            val radio = holder.getView<RadioButton>(R.id.rbCheck)
            val clickView = holder.getView<View>(R.id.clickView)
            ImageUtil.load(mContext, payBean.logoUrl, logoView)
            tvBankName.text = payBean.payName
            radio.isChecked = payBean.isSelected
            radio.isEnabled = payBean.canUse
            clickView.setOnClickListener {
                popWindowSelectCardCallback?.invoke(bean.payId?: "")
                dismiss()
            }
        }


    }

}

