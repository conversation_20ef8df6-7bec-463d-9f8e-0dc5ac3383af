package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybmmarket20.R

class BankCardConstraintLayout(context: Context, attr: AttributeSet?) :
    ConstraintLayout(context, attr) {

    var isOverlay = false

    override fun onLayout(changed: <PERSON>olean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
//        try {
//            val rv = findViewById<RecyclerView>(R.id.rvBankCard)
//            val view = findViewById<ConstraintLayout>(R.id.rclBottom)
//            val location = IntArray(2)
//            view.getLocationOnScreen(location)
//            val viewBottom = location[1].toFloat() + ScreenUtils.dip2px(context, 64F)
//            if (viewBottom > ScreenUtils.getScreenHeightWithoutStatusBar(context)) {
//                //超出
//                view.layout(
//                    ScreenUtils.dip2px(context, 10F),
//                    ScreenUtils.getScreenHeightWithoutStatusBar(context) - ScreenUtils.dip2px(context, 64F),
//                    ScreenUtils.getScreenWidth(context) - ScreenUtils.dip2px(context, 10F),
//                    ScreenUtils.getScreenHeightWithoutStatusBar(context)
//                )
//                if (!isOverlay) {
//                    val rvHeight = location[1] - ScreenUtils.getStatusBarHeight(context) - ScreenUtils.dip2px(context, 44F)
//                    val lp = rv.layoutParams as LayoutParams
//                    lp.height = rvHeight
//                    rv.layoutParams = lp
//                    isOverlay = true
//                }
//            } else {
//                if (isOverlay) {
//                    isOverlay = false
//                    val lp = rv.layoutParams as LayoutParams
//                    lp.height = ViewGroup.LayoutParams.WRAP_CONTENT
//                    rv.layoutParams = lp
//                }
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val rv = findViewById<RecyclerView>(R.id.rvBankCard)
        val remainHeight = ScreenUtils.getScreenHeightWithoutStatusBar(context) - ScreenUtils.dip2px(context, 44F) - ScreenUtils.dip2px(context, 64F) - 10
        if (remainHeight == rv.measuredHeight) return
        if(remainHeight < rv.measuredHeight) {
            val lp = rv.layoutParams
            lp.height = remainHeight
            rv.layoutParams = lp
        } else {
            val lp = rv.layoutParams
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT
            rv.layoutParams = lp
        }
    }
}