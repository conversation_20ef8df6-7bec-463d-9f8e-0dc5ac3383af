package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.PopupWindow;

import com.ybm.app.common.SmartExecutorManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.SuggestAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.SuggestBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 搜索的推荐
 */

public class SuggestPopWindow {
    private String url;
    private RequestParams params;
    private ItemClickListener listener;
    private List<SuggestBean> suggestBeanList;
    private PopupWindow popwindow;
    private LinearLayout contentView;
    private View token;
    private boolean cancelHandler;
    private SuggestAdapter suggestAdapter;
    private long lastTime = 0;
    private long diffTime = 800;
    private long MAXTIME = 800;
    private View.OnTouchListener onTouchListener;
    private String merchantid;

    public SuggestPopWindow(Context context, String url, RequestParams params, View token) {
        this.url = url;
        this.params = params;
        this.token = token;
        init(context);
    }

    private void init(Context context) {

        suggestBeanList = new ArrayList<>();
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        contentView = (LinearLayout) inflater.inflate(R.layout.list_suggest, null, false);
        ListView listView = (ListView) contentView.findViewById(R.id.listView);
        suggestAdapter = new SuggestAdapter(suggestBeanList, listView.getContext());
        listView.setAdapter(suggestAdapter);
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (listener != null && suggestBeanList != null && suggestBeanList.size() >= position) {
                    dismiss();
                    lastTime = System.currentTimeMillis();
                    listener.onItemClick(suggestBeanList.get(position).showName, suggestBeanList.get(position).id, position);
                }
            }
        });
    }

    public void setOnTouchListener(View.OnTouchListener listener){
        this.onTouchListener = listener;
    }

    public void setItemClickListener(ItemClickListener listener) {
        this.listener = listener;
    }

    public interface ItemClickListener {
        void onItemClick(String str, int id, int position);
    }


    public void suggest(final String str) {
        dismiss();
        if (System.currentTimeMillis() - lastTime >= diffTime) {
            getSuggestList(str, sugestHandler);
        } else {
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    if (System.currentTimeMillis() - lastTime >= MAXTIME) {
                        getSuggestList(str, sugestHandler);
                    }
                }
            }, MAXTIME);
        }
        lastTime = System.currentTimeMillis();
    }


    private void getSuggestList(String keyword, BaseResponse handler) {
        if (!TextUtils.isEmpty(keyword) && !cancelHandler) {
            if (params != null) {
                merchantid = SpUtil.getMerchantid();
                params.put("merchantId", merchantid);
                params.put("skuName", keyword);

            }
            HttpManager.getInstance().post(url, params, handler);
        }else {
            if(isShow()) {
                updateData(null);
            }
        }
    }

    private BaseResponse sugestHandler = new BaseResponse<List<SuggestBean>>() {

        @Override
        public void onSuccess(String content, BaseBean<List<SuggestBean>> obj, List<SuggestBean> suggestBean) {

            if (obj != null && obj.isSuccess()&&!cancelHandler) {
                show(token, suggestBean);
            }
        }
    };

    private void initPop(View token) {
        popwindow = new PopupWindow(contentView,
                token.getWidth(), WindowManager.LayoutParams.MATCH_PARENT, false);
        popwindow.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#ee222222")));
        popwindow.setOutsideTouchable(true);
        popwindow.setAnimationStyle(R.style.PopupWindowAnimation);

        popwindow.setInputMethodMode(PopupWindow.INPUT_METHOD_NEEDED);
        popwindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        popwindow.setTouchInterceptor(onTouchListener);
    }

    private void show(View token, List<SuggestBean> list) {
        if (popwindow == null) {
            initPop(token);
        }
        try {
            if (isShow()) {
                updateData(list);
                return;
            }
        } catch (Exception e) {
            return;
        }
        try {
            if (updateData(list)) {
                if (Build.VERSION.SDK_INT >= 24) {
                    int[] location = new int[2];
                    token.getLocationOnScreen(location);
                    // 7.1 版本处理
                    if (Build.VERSION.SDK_INT == 25) {
                        //【note!】Gets the screen height without the virtual key
                        WindowManager wm = (WindowManager) popwindow.getContentView().getContext().getSystemService(Context.WINDOW_SERVICE);
                        int screenHeight = wm.getDefaultDisplay().getHeight();
                        popwindow.setHeight(screenHeight - location[1] - token.getHeight());
                    }
                    popwindow.showAtLocation(token, Gravity.NO_GRAVITY, 0, location[1] + token.getHeight());
                }else {
                    popwindow.showAsDropDown(token, 0, 0);
                }
                popwindow.showAsDropDown(token, 0, 0);
            }
        } catch (Exception e) {
            return;
        }
        popwindow.update();
    }

    private boolean updateData(List<SuggestBean> list) {
        suggestBeanList.clear();
        if (list == null || list.size() <= 0) {
            suggestAdapter.notifyDataSetChanged();
            dismiss();
            return false;
        } else {
            suggestBeanList.addAll(list);
            suggestAdapter.notifyDataSetChanged();
        }
        if (list.size() <= 9) {
            popwindow.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        } else {
            popwindow.setHeight(UiUtils.getScreenHeight() * 3 / 4);
        }
        return true;
    }

    public void dismiss() {
        if (popwindow != null) {
            try {
                popwindow.dismiss();
            } catch (Exception e) {
            }
        }
    }

    public boolean isShow() {
        if (popwindow == null) {
            return false;
        }
        return popwindow.isShowing();
    }

    public void cancelHandler(boolean cancel){
        this.cancelHandler = cancel;
    }
}

