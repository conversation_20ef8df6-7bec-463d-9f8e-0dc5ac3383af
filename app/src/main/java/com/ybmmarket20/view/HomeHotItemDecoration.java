package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Rect;
import androidx.recyclerview.widget.RecyclerView;

import com.ybmmarket20.utils.UiUtils;

/**
 * <AUTHOR> Brin
 * @date : 2019/7/19 - 9:46
 * @Description :
 */
public class HomeHotItemDecoration extends RecyclerView.ItemDecoration {



    public HomeHotItemDecoration(Context context) {

    }


    @Override
    public void onDraw(Canvas c, RecyclerView parent) {

    }


    public void drawHorizontal(Canvas c, RecyclerView parent) {

    }


    @Override
    public void getItemOffsets(Rect outRect, int itemPosition, RecyclerView parent) {
        outRect.set(0, 0, UiUtils.dp2px(10), 0);
    }


}
