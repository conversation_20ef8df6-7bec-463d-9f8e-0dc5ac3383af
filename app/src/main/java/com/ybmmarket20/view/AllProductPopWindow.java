package com.ybmmarket20.view;

import android.view.View;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.OneRowsBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;

/**
 * 全部药品弹出popwindow
 */

public class AllProductPopWindow extends BaseFilterPopWindow {

    private ProductCategoryView mPcvList;

    @Override
    protected int getLayoutId() {
        return R.layout.pop_layout_all_product;
    }

    @Override
    protected void initView() {

        mPcvList = getView(R.id.pcv_list);

        mPcvList.setOnSelectListener(new ProductCategoryView.OnSelectListener() {
            @Override
            public void getValue(OneRowsBean bean) {

                if (mOnSelectListener != null) {
                    mOnSelectListener.getValue(new SearchFilterBean(bean.nickname, bean.id + ""));
                }

            }
        });
        getView(R.id.btn_affirm).setOnClickListener(v -> dismiss());
        getView(R.id.btn_reset).setOnClickListener(v -> {
            resetPosition();
        });
    }

    //重置商品分类
    public void resetPosition() {
        if (mPcvList != null) {
            mPcvList.resetAllPosition();
        }
        if (mOnSelectListener != null) {
            mOnSelectListener.getValue(null);
        }
    }

    public void show(View token) {
        if (mPcvList != null) {
            mPcvList.doGetNewsContent();
        }
        super.show(token);
    }

    public void show2(View token, String id) {
        if (mPcvList != null) {
            mPcvList.doGetNewsContent(1, "planningScheduleId", id);
        }
        super.show(token);
    }

    public void showShopCategory(View token, String orgId) {
        RequestParams requestParams = new RequestParams();
        requestParams.setUrl(AppNetConfig.SHOP_GOODS_CATEGORY);
        requestParams.put("merchantId", SpUtil.getMerchantid());
        requestParams.put("orgId", orgId);
        mPcvList.getContentByUrl(requestParams);
        super.show(token);
    }

    public void showShopCategory(String orgId) {
        RequestParams requestParams = new RequestParams();
        requestParams.setUrl(AppNetConfig.SHOP_GOODS_CATEGORY);
        requestParams.put("merchantId", SpUtil.getMerchantid());
        requestParams.put("orgId", orgId);
        mPcvList.getContentByUrl(requestParams);
    }

    public void setOnLevelItemClickListener(ProductCategoryView.LevelItemClickListener levelItemClickListener) {
        if (levelItemClickListener == null) return;
        mPcvList.setOnLevelItemClickListener(levelItemClickListener);
    }

}
