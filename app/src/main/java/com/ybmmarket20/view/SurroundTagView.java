package com.ybmmarket20.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import androidx.appcompat.widget.AppCompatTextView;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;

import com.ybmmarket20.R;

/**
 * Created by wh on 2018/3/1.
 */
//文字标签被内容包裹，换行能到行首
public class SurroundTagView extends AppCompatTextView {

    private float mTagWidth;
    private float mRadio, mDelta;
    private Paint mBgPaint;
    private Paint mTextPaint;
    private final String mEmpty = " ";

    private int mTagColor;
    private String mTagText;
    private int mTagBackground;
    private String mContentText;

    public SurroundTagView(Context context) {
        this(context, null);
    }

    public SurroundTagView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SurroundTagView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.SurroundTagView);
        mTagColor = ta.getColor(R.styleable.SurroundTagView_tag_color, Color.WHITE);
        mTagText = ta.getString(R.styleable.SurroundTagView_tag_text);
        mTagBackground = ta.getColor(R.styleable.SurroundTagView_tag_background, Color.GRAY);
        mContentText = ta.getString(R.styleable.SurroundTagView_content_text);
        ta.recycle();
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        mTextPaint = getPaint();
        float height = mTextPaint.getFontMetrics().descent - mTextPaint.getFontMetrics().ascent;
        mDelta = mTextPaint.getFontMetrics().ascent - mTextPaint.getFontMetrics().top;
        mRadio = height / 2;
    }

    public void setTagContent(String tag, String content) {
        setTagContent(tag, content, mTagColor, mTagBackground);
    }

    public void setTagContent(String tag, String content, int color, int background) {
        mTagColor = color;
        mTagBackground = background;
        mTagText = tag;
        mContentText = content;
        resetView();
    }

    private void resetView() {
        if (TextUtils.isEmpty(mTagText)) {
            return;
        }
        mBgPaint.setColor(mTagBackground);
        float perWidth = mTextPaint.measureText(mEmpty);
        String tag = mTagText;
        for (int i = 1, k = (int) (mRadio / perWidth); i < k; i++) {
            tag = mEmpty + tag + mEmpty;
        }
        mTagWidth = mTextPaint.measureText(tag) - 2 * mRadio;
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        ssb.append(tag).append(TextUtils.isEmpty(mContentText) ? mEmpty : mContentText);
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(mTagColor);
        ssb.setSpan(colorSpan, 0, tag.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
        setText(ssb);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        int left = getPaddingLeft();//左起始点
        int top = getPaddingTop();//上起始点
        float leftCenter = left + mRadio;//左圆心
        canvas.drawCircle(leftCenter, top + mRadio + mDelta, mRadio, mBgPaint);
        canvas.drawCircle(leftCenter + mTagWidth, top + mRadio + mDelta, mRadio, mBgPaint);
        canvas.drawRect(leftCenter, top + mDelta, leftCenter + mTagWidth, top + mDelta + 2 * mRadio, mBgPaint);
        super.onDraw(canvas);
    }
}
