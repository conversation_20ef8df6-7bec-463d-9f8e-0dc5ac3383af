package com.ybmmarket20.view;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;

public class ShowAptitudeBottomAddImageDialog extends BaseShowBottomSheetDialog {

    private TextView tv_taking_pictures;
    private TextView tv_photo_gallery;
    private TextView tv_cancel;

    public ShowAptitudeBottomAddImageDialog(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.dialog_layout_aptitude_add_image_item;
    }

    @Override
    protected void initView() {

        tv_taking_pictures = getView(R.id.tv_taking_pictures);
        tv_photo_gallery = getView(R.id.tv_photo_gallery);
        tv_cancel = getView(R.id.tv_cancel);

    }

    public void setOnTakingPicturesClickListener(View.OnClickListener listener) {

        if (tv_taking_pictures != null) {
            tv_taking_pictures.setOnClickListener(listener);
        }
    }

    public void setOnPhotoGalleryClickListener(View.OnClickListener listener) {

        if (tv_photo_gallery != null) {
            tv_photo_gallery.setOnClickListener(listener);
        }
    }

    public void setOnCancelClickListener(View.OnClickListener listener) {

        if (tv_cancel != null) {
            tv_cancel.setOnClickListener(listener);
        }
    }


    @Override
    protected int getListH() {
        return 0;
    }

    @Override
    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }
}
