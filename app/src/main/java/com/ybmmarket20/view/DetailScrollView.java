package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Point;
import android.util.AttributeSet;
import android.view.WindowManager;
import android.widget.ScrollView;

import com.apkfuns.logutils.LogUtils;

import java.util.ArrayList;

public class DetailScrollView extends ScrollView {


    private final Point point;
    private CommodityBannerLayout viewPager;

    private int position = 0;

    ArrayList<Integer> arrayDistance = new ArrayList<>();
    private int headerHeight;

    public DetailScrollView(Context context) {
        this(context, null, 0);
    }

    public DetailScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DetailScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        point = new Point();
        windowManager.getDefaultDisplay().getSize(point);
    }

    //重新onScrollChanged函数，实现ViewPager滑动速度比其他View慢
    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);

        //根据限定距离（Banner）计算百分比偏移量，实现颜色渐变、透明度渐变（淘宝商品详情页有二次颜色渐变）
        if (viewPager != null && t <= point.x - headerHeight && getOnScrollChangedColorListener() != null) {

            getOnScrollChangedColorListener().onChanged(Math.abs(t) / Float.valueOf(point.x - headerHeight));
            if (t <= (point.x - headerHeight) / 2) {
                getOnScrollChangedColorListener().onChangedFirstColor(t / (point.x - headerHeight) / 2);
            } else {
                getOnScrollChangedColorListener().onChangedSecondColor((t - (point.x - headerHeight) / 2) / (point.x - headerHeight) / 2);
            }

        }

        int currentPosition = getCurrentPosition(t, arrayDistance);
        if (currentPosition != position && getOnSelectedIndicateChangedListener() != null) {
            getOnSelectedIndicateChangedListener().onSelectedChanged(currentPosition);
        }
        this.position = currentPosition;
    }

    //判断当前属于哪个选项，根据滑动距离与传入绑定的View高度集合来计算
    private int getCurrentPosition(int t, ArrayList<Integer> arrayDistance) {

        int index = 0;
        for (int i = 0; i < arrayDistance.size(); i++) {
            if (i == arrayDistance.size() - 1) {
                index = i;
            } else {
                if (t >= arrayDistance.get(i) && t < arrayDistance.get(i + 1)) {
                    index = i;
                    break;
                }
            }
        }
        return index;
    }

    private void scrollToPosition() {
        scrollToPosition(position);
    }

    //切换选项卡以及回到顶部按钮的具体实现参考scrollTo函数
    private void scrollToPosition(int position) {
        if (arrayDistance == null || arrayDistance.size() <= 0) {
            return;
        }
        scrollTo(0, arrayDistance.get(position));
    }

    public void setViewPager(CommodityBannerLayout viewPager, int headerHeight) {
        this.viewPager = viewPager;
        this.headerHeight = headerHeight;
    }

    public interface OnScrollChangedColorListener {

        void onChanged(float percentage);

        void onChangedFirstColor(float percentage);

        void onChangedSecondColor(float percentage);

    }

    public interface OnSelectedIndicateChangedListener {

        void onSelectedChanged(int position);
    }

    private OnSelectedIndicateChangedListener onSelectedIndicateChangedListener;

    private OnScrollChangedColorListener onScrollChangedColorListener;

    public OnScrollChangedColorListener getOnScrollChangedColorListener() {
        return onScrollChangedColorListener;
    }

    public void setOnScrollChangedColorListener(OnScrollChangedColorListener onScrollChangedColorListener) {
        this.onScrollChangedColorListener = onScrollChangedColorListener;
    }

    public CommodityBannerLayout getViewPager() {
        return viewPager;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
        scrollToPosition();
    }

    public ArrayList<Integer> getArrayDistance() {
        return arrayDistance;
    }

    public void setArrayDistance(ArrayList<Integer> arrayDistance) {
        this.arrayDistance = arrayDistance;
    }

    public OnSelectedIndicateChangedListener getOnSelectedIndicateChangedListener() {
        return onSelectedIndicateChangedListener;
    }

    public void setOnSelectedIndicateChangedListener(OnSelectedIndicateChangedListener onSelectedIndicateChangedListener) {
        this.onSelectedIndicateChangedListener = onSelectedIndicateChangedListener;
    }
}
