package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout.LayoutParams;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.UiUtils;
import com.ybmmarket20.R;
import com.ybmmarket20.common.util.Abase;

import java.io.File;

public class ShowBigBitmapPopPublish {
	private PopupWindow popwindow;
	private View view_big_bitmap;
	private ImageView iv_big_bitmap;
	private TextView tv_del_img;
	private LinearLayout ll_del_pic;
	private Context context;
	private boolean detele;
	private ImageView ivBigImage;

	public ShowBigBitmapPopPublish(String picPath){
		this(picPath, 0, null);
	}
	public ShowBigBitmapPopPublish(String picPath, final int pos,
								   final RefreshGvListener listener){
		this(picPath, pos, listener,BaseYBMApp.getAppContext());
	}

	public ShowBigBitmapPopPublish(String picPath, final int pos,
								   final RefreshGvListener listener, Context context) {
		int height = UiUtils.getScreenHeight()-50;
		int width = UiUtils.getScreenWidth()-50;
		if(context!=null){
			this.context = context;
		}else {
			this.context = BaseYBMApp.getAppContext();
		}
		view_big_bitmap = LayoutInflater.from(this.context).inflate(
				R.layout.view_big_bitmap_publish, null);
		iv_big_bitmap = (ImageView) view_big_bitmap
				.findViewById(R.id.iv_big_bitmap);
		tv_del_img = (TextView) view_big_bitmap
				.findViewById(R.id.tv_del_img);
		ll_del_pic = (LinearLayout) view_big_bitmap.findViewById(R.id.ll_del_pic);
		if(listener != null) {
			ll_del_pic.setVisibility(View.VISIBLE);
		}else {
			ll_del_pic.setVisibility(View.GONE);
		}
		if(picPath.startsWith("http") || picPath.startsWith("Http")){
			ImageHelper.with(context).load(picPath).asBitmap().dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(iv_big_bitmap);
		}else{
			ImageHelper.with(context).load(new File(picPath)).asBitmap().skipMemoryCache(true).dontAnimate().dontTransform().into(iv_big_bitmap);
		}
		popwindow = new PopupWindow(view_big_bitmap,
				LayoutParams.FILL_PARENT, LayoutParams.FILL_PARENT, true);
		popwindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
		popwindow.setAnimationStyle(R.style.AnimBottom);
		
		tv_del_img.setOnClickListener(new OnClickListener() {
			
			@Override
			public void onClick(View arg0) {
				popwindow.dismiss();
				if (listener != null) {
					listener.refreshGv(pos);
				}
			}
		});
		iv_big_bitmap.setOnClickListener(new OnClickListener() {
					
				@Override
				public void onClick(View arg0) {
					popwindow.dismiss();
				}
		});
	}
	/**
	 * 加载本地资源
	 *
	 * @param res
	 */
	public ShowBigBitmapPopPublish(int res) {
		this.context = Abase.getContext();
		View viewBigBitmap = LayoutInflater.from(this.context).inflate(R.layout.view_big_bitmap_publish, null);
		ivBigImage = viewBigBitmap.findViewById(R.id.iv_big_bitmap);
		viewBigBitmap.findViewById(R.id.ll_del_pic).setVisibility(View.GONE);
		popwindow = new PopupWindow(viewBigBitmap, LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT, true);
		//sdk > 21 解决 标题栏没有办法遮罩的问题
		popwindow.setClippingEnabled(false);
		popwindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
		popwindow.setAnimationStyle(R.style.AnimBottom);
		ivBigImage.setImageResource(res);
		ivBigImage.setOnClickListener(arg0 -> popwindow.dismiss());
	}

	public void show(View parent) {
		if (popwindow == null) {
			return;
		}
		if (popwindow.isShowing())
			popwindow.dismiss();
		else {
			popwindow.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
			popwindow.update();
		}
	}
	
	public static interface RefreshGvListener{
		public void refreshGv(int pos);
	}
}
