package com.ybmmarket20.view

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.CountDownTimer
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.core.content.ContextCompat
import android.util.DisplayMetrics
import android.view.*
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.activity.ChangeMobilePhoneActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ResponseCodeBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.TimeCountUtil
import kotlinx.android.synthetic.main.verification_code_pop_layout.view.*


@SuppressLint("ValidFragment")
/**
 * @author: yuhaibo
 * @time: 2019-08-15 10:48.
 * projectName: ybm-android.
 * scription: 企业被委托人身份信息确认
 */
class VerificationCodeDialog(private val raAuthMsg: String?, val merchantId: String?, val orderId: String?) : DialogFragment() {
    private var mCountDownTimer: CountDownTimer? = null
    private var smsCode: String = ""

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val dm = DisplayMetrics()
        activity?.windowManager?.defaultDisplay?.getMetrics(dm)
        window?.setLayout((dm.widthPixels * 0.72).toInt(), ViewGroup.LayoutParams.WRAP_CONTENT)
        dialog?.setCanceledOnTouchOutside(false)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)//除掉弹出框的标题
        val dialogView = inflater.inflate(R.layout.verification_code_pop_layout, container)
        dialogView?.tv_describe?.text = raAuthMsg

        dialogView?.btn_cancel?.setOnClickListener {
            startActivity(Intent(activity, ChangeMobilePhoneActivity::class.java))
            dismiss()
        }
        dialogView?.btn_confirm?.setOnClickListener { loadCertificationData(dialogView?.verificationView) }
        //重新获取验证码
        dialogView?.tv_countdown?.setOnClickListener {
            handleCountdown(dialogView)
            getPhoneNumSendCodeData()
        }
        dialogView?.btn_close?.setOnClickListener { dismiss() }
        //倒计时
        handleCountdown(dialogView)
        dialogView?.verificationView?.listener = { s, isInputFinish ->
            if (isInputFinish) {
                smsCode = s
                dialogView.btn_confirm?.setBackgroundColor(resources.getColor(R.color.color_00B377))
            } else {
                dialogView.btn_confirm?.setBackgroundColor(resources.getColor(R.color.color_A9AEB7))
            }
            dialogView?.btn_confirm?.isEnabled = isInputFinish
        }
        //关闭监听
        dialog?.setOnDismissListener {
            destroy()
        }
        return dialogView
    }

    override fun onDestroy() {
        super.onDestroy()
        destroy()
    }

    private fun handleCountdown(dialogView: View) {
        mCountDownTimer = TimeCountUtil(dialogView?.tv_countdown, 120000, 1000)
                .setCountdownListener(object : TimeCountUtil.CountdownListener {
                    override fun onCountdownIngListener(view: TextView?, secondsNum: String) {
                        view?.text = "重新发送(${secondsNum}s)"
                        activity?.apply {
                            view?.setTextColor(ContextCompat.getColor(this, R.color.color_676773))
                        }
                    }

                    override fun onCountdownFinishListener(view: TextView?) {
                        activity?.apply {
                            view?.setTextColor(ContextCompat.getColor(this, R.color.color_00B377))
                        }
                    }
                }).start()
    }


    /**
     * 验证确认
     */
    private fun loadCertificationData(verificationView: VerificationView?) {
        val params = RequestParams
                .newBuilder()
                .url(AppNetConfig.CERTIFICATION)
                .addParam("merchantId", merchantId)
                .addParam("orderId", orderId)
                .addParam("smsCode", smsCode)
                .addParam("uuid", com.ybmmarket20.utils.SpUtil.getDeviceId())
                .build()
        HttpManager.getInstance().post(params, object : BaseResponse<ResponseCodeBean>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<ResponseCodeBean>?, t: ResponseCodeBean?) {
                super.onSuccess(content, baseBean, t)
                //code 10000 成功 40000 失败
                ToastUtils.setGravity(Gravity.CENTER, 0, 0)
                ToastUtils.showShort(t?.msg)
                verificationView?.clear()
                if (t?.code == 10000) {
                    dismiss()
                }
                verifyListener?.invoke(t?.code == 10000)
            }
        })
    }

    /**
     * 重新获取验证码
     */
    private fun getPhoneNumSendCodeData() {
        val params = RequestParams
                .newBuilder()
                .url(AppNetConfig.AGAIN_SENDCODE)
                .addParam("merchantId", merchantId)
                .addParam("orderId", orderId)
                .addParam("uuid", com.ybmmarket20.utils.SpUtil.getDeviceId())
                .build()
        HttpManager.getInstance().post(params, object : BaseResponse<ResponseCodeBean>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<ResponseCodeBean>?, t: ResponseCodeBean?) {
                super.onSuccess(content, baseBean, t)
                ToastUtils.setGravity(Gravity.CENTER, 0, 0)
                ToastUtils.showShort(t?.msg)
            }
        })
    }

    private fun destroy() {
        mCountDownTimer?.cancel()
        mCountDownTimer = null
    }

    companion object {
        var verifyListener: ((Boolean) -> Unit)? = null
        fun show(fragmentManager: FragmentManager?, raAuthMsg: String?, merchantId: String?, orderId: String?) {
            val ft = fragmentManager?.beginTransaction()
            val prev = fragmentManager?.findFragmentByTag("VerificationCodeDialog")
            if (prev != null) {
                ft?.remove(prev)
            }
            ft?.addToBackStack(null)
            val newFragment = VerificationCodeDialog(raAuthMsg, merchantId, orderId)
            try {
                if (newFragment.isAdded) {
                    newFragment.dismiss()
                } else {
                    ft?.let { newFragment.show(it, "VerificationCodeDialog") }
                }
            } catch (e: Exception) {
            }
        }
    }

}