package com.ybmmarket20.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import android.widget.TextView

/**
 * 创建人: yuhaibo
 * 创建时间: 2018/11/16 15:54.
 * 带有下划线和删除线的TextView
 */
@SuppressLint("AppCompatCustomView")
class UnderlineTextView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : TextView(context, attrs) {

    fun setText(text: CharSequence, linePosition: Int) {
        super.setText(text)
        paint.isAntiAlias = true
        when (linePosition) {
            MIDDLE -> paint.flags = Paint.STRIKE_THRU_TEXT_FLAG
            BOTTOM -> paint.flags = Paint.UNDERLINE_TEXT_FLAG
        }
    }

    companion object {
        const val MIDDLE = 0
        const val BOTTOM = 1
    }
}