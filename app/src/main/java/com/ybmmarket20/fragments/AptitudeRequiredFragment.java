package com.ybmmarket20.fragments;

import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AptitudeDetailBean;
import com.ybmmarket20.bean.AptitudeDetailListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.common.widget.RoundLinearLayout;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.ImageUtilKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.DefaultItemDecoration;
import com.ybmmarket20.view.ShowBigBitmapPopPublishForLongPic;
import com.ybmmarketkotlin.fragments.AptitudeOverdueDialogFragment;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;
import butterknife.OnClick;

import static com.ybmmarket20.activity.AptitudeActivity.EXTRA_APTITUDE_TABINDEX;
import static com.ybmmarket20.activity.AptitudeActivity.TYPE_APTITUDE_REQUIRED;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_LICENSE_XYY_DOWN_STATUS;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_NET_ERR;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_UPDATE_LICENCEDTAIL;

/**
 * 资质列表
 */
public class AptitudeRequiredFragment extends BaseFragment {
    @Bind(R.id.rcv_aptitude)
    RecyclerView mList;
    @Bind(R.id.tv_customer_phone)
    TextView tvCustomerPhone;
    @Bind(R.id.tv_customer_phone2)
    TextView tvCustomerPhone2;
    @Bind(R.id.tv_aptitude_required_top_hint)
    TextView tvTopHint;

    @Bind(R.id.ll_content)
    LinearLayout ll_content;
    @Bind(R.id.layout_load_error)
    ViewGroup layout_load_error;
    @Bind(R.id.tv_download_more)
    TextView tvDownloadMore;
    @Bind(R.id.tv_bottom_tips)
    TextView tvBottomTips;

    private YBMBaseAdapter adapter;
    private List<AptitudeDetailListBean> list = new ArrayList<>();
    private SimpleDateFormat dateFormat;

    private int index;
    private TextView tvError;
    private View emptyView;
    private ImageView iv;


    @Override
    protected void initData(String content) {
        index = getArguments().getInt(EXTRA_APTITUDE_TABINDEX);
        if (index == TYPE_APTITUDE_REQUIRED) {//资质列表
            //url=AppNetConfig.FIND_LICENSE_LIST;
        }
        initRecyclerView();
        getAptitudeInfo();
    }

    private void initRecyclerView() {

        dateFormat = new SimpleDateFormat("yyyy.MM.dd", Locale.getDefault());
        //资质列表
        adapter = new YBMBaseAdapter<AptitudeDetailListBean>(R.layout.item_aptitude_updated, list) {
            @Override
            protected void bindItemView(YBMBaseHolder ybmBaseHolder, AptitudeDetailListBean bean) {
                String validateTime = dateFormat.format(new Date(bean.validUntil));
                ybmBaseHolder.setText(R.id.tv_aptitude_name, bean.credentialName)
                        .setText(R.id.tv_aptitude_time, "有效期至：" + validateTime);

                ImageView ivAptitudePic = ybmBaseHolder.getView(R.id.iv_aptitude_pic);
                if (bean.enclosureList != null && bean.enclosureList.size() > 0) {
                    ImageUtil.load(getContext(), bean.enclosureList.get(0).url, ivAptitudePic);
                    ivAptitudePic.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            new ShowBigBitmapPopPublishForLongPic(bean.enclosureList.get(0).url).show(ivAptitudePic);
                        }
                    });
                }

                TextView tvAptitudeStatusTips = ybmBaseHolder.getView(R.id.tv_aptitude_status_tips);
                setTable(bean.status, bean.tips, tvAptitudeStatusTips);
            }

        };
        emptyView = getNotNullActivity().getLayoutInflater().inflate(R.layout.layout_empty_view, (ViewGroup) mList.getParent(), false);
        tvError = emptyView.findViewById(R.id.tv);
        iv = emptyView.findViewById(R.id.iv);
        tvError.setText("您还未添加首营资质");
        emptyView.setBackgroundColor(UiUtils.getColor(R.color.white));
        emptyView.setPadding(0, 0, 0, UiUtils.dp2px(20));
        iv.setOnClickListener(this::clickTab);
        adapter.setEmptyView(emptyView);
        DefaultItemDecoration defaultItemDecoration = new DefaultItemDecoration(getContext(), UiUtils.dp2px(1));
        defaultItemDecoration.setLineColorResId(R.color.color_divider_bg);
        mList.addItemDecoration(defaultItemDecoration);
        mList.setAdapter(adapter);
        mList.setLayoutManager(new WrapLinearLayoutManager(getContext()));
        mList.setNestedScrollingEnabled(false);
    }

    /**
     * 设置资质列表的过期提示
     *
     * @param tips 提示文案
     * @param tvAptitudeStatusTips
     */
    private void setTable(int status, String tips, TextView tvAptitudeStatusTips) {
        int colorRes = R.color.color_292933;

        switch (status) {
            // 0：正常 1：过期 2：临期
            //已提交
            case 1:
                colorRes = R.color.color_text_status_overtime;
                tvAptitudeStatusTips.setVisibility(View.VISIBLE);
                tvAptitudeStatusTips.setText(tips);
                break;
            case 2:
                colorRes = R.color.color_text_status_bytime;
                tvAptitudeStatusTips.setVisibility(View.VISIBLE);
                tvAptitudeStatusTips.setText(tips);
                break;
            default:
                tvAptitudeStatusTips.setVisibility(View.GONE);
        }
        tvAptitudeStatusTips.setTextColor(getResources().getColor(colorRes));
        if (!TextUtils.isEmpty(tips)) {
            tvAptitudeStatusTips.setText(tips);
            tvAptitudeStatusTips.setVisibility(View.VISIBLE);
        } else {
            tvAptitudeStatusTips.setVisibility(View.GONE);
        }
    }

    public void getAptitudeInfo() {
        if (tvCustomerPhone == null) {
            return;
        }
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.LICENSE_AUDIT_FIND_LICENSE_LIST, params, new BaseResponse<AptitudeDetailBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
//                tvTopHint.setVisibility(View.GONE);
//                rlTops.setVisibility(View.GONE);
//                tvError.setText("网络加载失败，请点击重新加载");
//                iv.setImageDrawable(UiUtils.getDrawable(getNotNullActivity(),R.drawable.icon_no_network));
                ll_content.setVisibility(View.GONE);
                layout_load_error.setVisibility(View.VISIBLE);
                Event<Boolean> event = new Event<>(RX_BUS_NET_ERR, true);
                EventBusUtil.sendEvent(event);
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudeDetailBean> baseBean, AptitudeDetailBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null) {
                        if (!TextUtils.isEmpty(data.phone)) {
                            tvCustomerPhone.setText(data.phone);
                        }
                        if (data.licenseList != null && data.licenseList.size() > 0) {
                            if (list == null) {
                                list = new ArrayList<>();
                            }
                            list.clear();
                            list.addAll(data.licenseList);
                            adapter.notifyDataSetChanged();
                        }
                        if (ll_content != null) {
                            ll_content.setVisibility(View.VISIBLE);
                        }
                        if (layout_load_error != null) {
                            layout_load_error.setVisibility(View.GONE);
                        }
                        Event<Boolean> event = new Event<>(RX_BUS_NET_ERR, false);
                        EventBusUtil.sendEvent(event);
                    }
                }

            }
        });
    }


    @OnClick({R.id.ll_tv_customer_phone, R.id.ll_tv_customer_phone2, R.id.tv_download_more, R.id.tv_reload})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.tv_download_more://查看小药药资质
                // 2020-02-10  查看小药药资质
                RoutersUtils.open("ybmpage://aptitudexyy");
                JGTrackTopLevelKt.jgTrackAptitudeBtnClick(getContext(), "查看小药药资质");
                break;
            case R.id.ll_tv_customer_phone:
                String phone = tvCustomerPhone.getText().toString().trim();
                if (TextUtils.isEmpty(phone)) {
                    UiUtils.toast("电话为空");
                    return;
                }
                RoutersUtils.telPhone(true, phone);
                JGTrackTopLevelKt.jgTrackAptitudeBtnClick(getContext(), "客户经理");
                break;
            case R.id.ll_tv_customer_phone2:
                String phone2 = tvCustomerPhone2.getText().toString().trim();
                if (TextUtils.isEmpty(phone2)) {
                    UiUtils.toast("电话为空");
                    return;
                }
                RoutersUtils.telPhone(true, phone2);
                XyyIoUtil.track(XyyIoUtil.ACTION_ME_CUSTOMERSERVICETELEPHONE);
                JGTrackTopLevelKt.jgTrackAptitudeBtnClick(getContext(), "客户服务");
                break;
            case R.id.iv://重新加载
                getAptitudeInfo();
                break;
            case R.id.tv_reload://重新加载
                getAptitudeInfo();
                break;
        }
    }

    @Override
    protected boolean isRegisterEventBus() {
        return true;
    }

    @Override
    protected void receiveEvent(Event event) {
        if (event.getCode() == RX_BUS_UPDATE_LICENCEDTAIL) {
            boolean isSubscribe = (boolean) event.getData();
            if (isSubscribe) {//刷新数据
                getAptitudeInfo();
            }
        } else if (event.getCode() == RX_BUS_LICENSE_XYY_DOWN_STATUS) {//查看小药药资质
            int licenseDownStatus = (int) event.getData();
            // 2020-02-10 查看小药药资质
            if (licenseDownStatus == 1) {//1有权限显示 0无权限隐藏
                tvDownloadMore.setVisibility(View.VISIBLE);
            } else {
                tvDownloadMore.setVisibility(View.GONE);
            }

        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_layout_aptitude_required;
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

}
