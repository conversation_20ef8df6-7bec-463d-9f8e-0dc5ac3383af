package com.ybmmarket20.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.SmartExecutorManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.SelectInvitationInfoBean;
import com.ybmmarket20.bean.TheInvitationBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.BitmapView;
import com.ybmmarket20.view.MyScrollView;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 邀请
 */
@Router("theinvitation")
public class TheInvitationActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.tv_sms_invitation)
    TextView tvSmsInvitation;
    @Bind(R.id.tv_money_voucher)
    TextView tvMoneyVoucher;
    @Bind(R.id.tv_money_voucher_friends)
    TextView tvMoneyVoucherFriends;
    @Bind(R.id.tv_accumulated_coupon)
    TextView tvAccumulatedCoupon;
    @Bind(R.id.tv_successful_registration)
    TextView tvSuccessfulRegistration;
    @Bind(R.id.tv_successful_order)
    TextView tvSuccessfulOrder;

    Bitmap bitmap;
    MyScrollView sv;
    TextView tvDescribe;
    TextView tvCouponsDescribe;
    TextView tvCouponsNum;
    RoundTextView tvInviteCode;
    private TheInvitationBean data;

    private static final int REQUEST_EXTERNAL_STORAGE = 1;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_the_invitation;
    }

    @Override
    protected void initData() {
        setTitle("邀请");
        sendImage();
    }

    private void sendImage() {
        View v = LayoutInflater.from(this).inflate(R.layout.activity_invitation, null, false);
        sv = (MyScrollView) v.findViewById(R.id.sv);
        tvDescribe = (TextView) v.findViewById(R.id.tv_describe);
        tvCouponsDescribe = (TextView) v.findViewById(R.id.tv_coupons_describe);
        tvCouponsNum = (TextView) v.findViewById(R.id.tv_coupons_num);
        tvInviteCode = (RoundTextView) v.findViewById(R.id.tv_invite_code);

        BitmapView.layoutView(v, UiUtils.getScreenWidth(), UiUtils.getScreenHeight());
    }

    /*
     *
     * 获取数据
     * */
    private void getResult() {

        showProgress();
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        HttpManager.getInstance().post(AppNetConfig.THE_INVITATION, params, new BaseResponse<TheInvitationBean>() {

            @Override
            public void onSuccess(String content, BaseBean<TheInvitationBean> listBean, TheInvitationBean data) {
                if (tvSmsInvitation == null) {
                    return;
                }
                dismissProgress();
                if (listBean != null && listBean.isSuccess() && data != null) {
                    if (listBean.isSuccess()) {
                        setData(data);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    public void setData(TheInvitationBean data) {
        if (tvSmsInvitation == null) {
            return;
        }
        if (data == null) {
            return;
        }
        this.data = data;
        tvMoneyVoucher.setText(UiUtils.transformInt(data.moneyVoucherInviter));
        tvMoneyVoucherFriends.setText("好友最低获得" + UiUtils.transformInt(data.moneyVoucherInvitee) + "元");
        tvAccumulatedCoupon.setText(UiUtils.transformInt(data.incomeAmount));
        tvSuccessfulRegistration.setText(data.inviteCount + "");
        tvSuccessfulOrder.setText(data.orderCount + "");
    }

    @OnClick({R.id.tv_sms_invitation, R.id.tv_my_bonus_pool, R.id.tv_the_rules, R.id.tv_immediately_invited
            , R.id.tv_posters})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_sms_invitation:
                RoutersUtils.open("ybmpage://smsinvitation");
                XyyIoUtil.track(XyyIoUtil.ACTION_ME_INVITEDGIFTSMSINVITEFRIENDS);
                break;
            case R.id.tv_my_bonus_pool:
                RoutersUtils.open("ybmpage://bonuspools");
                XyyIoUtil.track(XyyIoUtil.ACTION_ME_INVITEDGIFTREWARDPOOLINVITATIONSUCCESSFUL);
                XyyIoUtil.track(XyyIoUtil.ACTION_ME_INVITEDGIFTREWARDPOOLREWARDS);
                break;
            case R.id.tv_the_rules:
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.THE_RULES);
                break;
            case R.id.tv_immediately_invited:
                DialogUtil.shareTheInvitationDialog(this, false, new DialogUtil.DialogSingleClick() {
                    @Override
                    public void click(String content) {
                        if (TextUtils.isEmpty(content)) {
                            return;
                        }
                        share(content);
                    }
                });
                break;
            case R.id.tv_posters:

                getPosters();
                break;
        }
    }

    private void getPosters() {

        showProgress();
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        HttpManager.getInstance().post(AppNetConfig.SELECT_INVITATION, params, new BaseResponse<SelectInvitationInfoBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SelectInvitationInfoBean> listBean, SelectInvitationInfoBean data) {
                if (tvSmsInvitation == null) {
                    return;
                }
                dismissProgress();
                if (listBean != null && listBean.isSuccess() && data != null) {
                    if (listBean.isSuccess()) {
                        setPosters(data);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    private void setPosters(SelectInvitationInfoBean data) {
        if (tvDescribe == null) {
            return;
        }
        String remainTimeStr = "0";
        try {
            String timerH = DateTimeUtil.getTimeDifferenceDay(data.auditTime, System.currentTimeMillis());
            if (!TextUtils.isEmpty(timerH)) {
                remainTimeStr = timerH;
            }
        } catch (Exception ext) {
            ext.printStackTrace();
        }
        tvDescribe.setText("Hi~我是" + data.userName + ", 我已经来到药帮忙" + remainTimeStr + "天");
        tvCouponsDescribe.setText("送你" + UiUtils.transformInt(data.moneyVoucher) + "元优惠券");
        tvCouponsNum.setText(UiUtils.transformInt(data.moneyVoucher));
        tvInviteCode.setText("邀请码：" + SpUtil.getMerchantid());


        if (!TextUtils.isEmpty(data.userName)) {
            DialogUtil.shareTheInvitationDialog(this, true, new DialogUtil.DialogSingleClick() {
                @Override
                public void click(String content) {
                    if (TextUtils.isEmpty(content)) {
                        return;
                    }

                    sharePosters(content);
                }
            });
        }
    }

    private void share(String platform) {
        if (!ShareUtil.isWeChatOrQQInstall(platform)) {
            return;
        }
        if (data == null) {
            return;
        }
        String title = "送你" + UiUtils.transformInt(data.moneyVoucherInvitee) + "元，优惠任你选～";
        String content = "邀请新用户，最低获得" + UiUtils.transformInt(data.moneyVoucherInviter) + "元!";
        String content_pyq = "我在药帮忙送了你" + UiUtils.transformInt(data.moneyVoucherInvitee) + "元，快来领取吧～";
        if ("wx".equals(platform)) {
            Bitmap bmp = BitmapFactory.decodeResource(getResources(), R.drawable.logo);
            ShareUtil.shareWXPage(0, title, AppNetConfig.THE_INVITATION_SHARE + SpUtil.getMerchantid(), content, bmp);
        } else if ("wxpyq".equals(platform)) {
            Bitmap bmp = BitmapFactory.decodeResource(getResources(), R.drawable.logo);
            ShareUtil.shareWXPage(1, content_pyq, AppNetConfig.THE_INVITATION_SHARE + SpUtil.getMerchantid(), content_pyq, bmp);
        }
    }

    private void sharePosters(String platform) {
        if (!ShareUtil.isWeChatOrQQInstall(platform)) {
            return;
        }

        if ("wx".equals(platform)) {
            bitmap = BitmapView.viewSave2ToImage(sv);
            if (bitmap != null) {
                ShareUtil.shareWXWithImage(0, bitmap, UiUtils.getScreenWidth(), UiUtils.getScreenHeight());
            }
        } else if ("wxpyq".equals(platform)) {
            bitmap = BitmapView.viewSave2ToImage(sv);
            if (bitmap != null) {
                ShareUtil.shareWXWithImage(1, bitmap, UiUtils.getScreenWidth(), UiUtils.getScreenHeight());
            }
        } else if ("bctp".equals(platform)) {

            if (FileUtil.isOpenVerifyStoragePermissions(TheInvitationActivity.this)) {

                SmartExecutorManager.getInstance().execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            BitmapView.viewSaveToImage(sv, TheInvitationActivity.this);

                        } catch (Exception e) {
                            e.printStackTrace();
                            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtils.showShort("图片保存出错");
                                }
                            });
                        }
                    }
                });
            }
        }

    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[]
            grantResults) {
        switch (requestCode) {
            case REQUEST_EXTERNAL_STORAGE:
                switch (permissions[0]) {
                    case Manifest.permission.WRITE_EXTERNAL_STORAGE://权限1
                        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {

                        } else {
                            ToastUtils.showShort("保存图片需要打开存储权限，请前往“设置”打开药帮忙存储权限");
                        }
                        break;
                    default:
                }
                break;
            default:
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        getResult();
    }

}
