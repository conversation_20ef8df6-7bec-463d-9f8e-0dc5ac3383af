package com.ybmmarket20.activity

import android.animation.Animator
import android.animation.ObjectAnimator
import android.graphics.Color
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.analysys.ANSAutoPageTracker
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.adapter.AddCartCallback
import com.ybmmarket20.adapter.SpellGroupCommendSelectedGoodsCategoryAdapter
import com.ybmmarket20.adapter.SpellGroupRecommendSelectedGoodsAdapter
import com.ybmmarket20.adapter.SpellGroupRecommendSelectedGoodsCartsAdapter
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackSpellGroupRecommendSelectedGoodsBtnClick
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.OPENNEXTPAGEURLKEY
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.ImageUtil.Companion.loadRoundCornerImage
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.CURRENT_PAGE_SELECT_RECOMMEND_GOODS
import com.ybmmarket20.viewmodel.ShopHomeViewModel
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.SpellGroupRecommendSelectViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore
import com.ybmmarketkotlin.bean.ShopBaseInfo
import kotlinx.android.synthetic.main.activity_spell_group_recommend_selected_goods.*
import kotlinx.android.synthetic.main.layout_spell_group_recommend_selected_goods_cart_detail.*
import kotlinx.android.synthetic.main.layout_spell_group_recommend_selected_goods_content.*
import kotlinx.android.synthetic.main.layout_spell_group_recommend_selected_goods_header.*
import kotlinx.android.synthetic.main.layout_spell_group_recommend_selected_goods_title_bar.*

/**
 * 拼团选购品
 */
@Router("spellgrouprecommendselectedgoodsactivity")
class SpellGroupRecommendSelectedGoodsActivity : BaseActivity(), View.OnClickListener, ANSAutoPageTracker {

    private var mViewModel: SpellGroupRecommendGoodsViewModel? = null
    private lateinit var mShopInfoModel: ShopHomeViewModel
    private val spellGroupRecommendSelectViewModel: SpellGroupRecommendSelectViewModel by viewModels()


    // 自营店铺数据
    private var selfShopInfo: ShopBaseInfo? = null

    // pop店铺数据
    private var popShopInfo: ShopBasicInfoBean? = null

    // pop客服链接
    private var popImUrl: String? = null

    private var searchMoreParams: RequestParams? = null


    // 品类数据
    var floorData: MutableList<ShopHomeIndexBean.Floor> = mutableListOf()
    private lateinit var categoryAdapter: SpellGroupCommendSelectedGoodsCategoryAdapter

    var floorId: String? = null

    // 商品数据
    var goodsData: MutableList<RowsBean> = mutableListOf()
    var goodlistAdapter: SpellGroupRecommendSelectedGoodsAdapter? = null

    var property: String? = "smsr.sale_num"

    var cartsAdapter: SpellGroupRecommendSelectedGoodsCartsAdapter? = null
    private var mIsWholeSale: String? = ""
    var jgTrackBean: JgTrackBean? = null

    override fun getContentViewId(): Int {
        return R.layout.activity_spell_group_recommend_selected_goods
    }

    override fun initData() {

        mShopInfoModel = ViewModelProvider(this).get(ShopHomeViewModel::class.java)

        mViewModel = ViewModelProvider(
            GlobalViewModelStore.get().getGlobalViewModelStore(),
            SavedStateViewModelFactory(application, this)
        ).get(SpellGroupRecommendGoodsViewModel::class.java)
//        try {
//            var rowJson = RoutersUtils.getStringFromBase64(intent.getStringExtra("rowJson"))
//            rowJson = rowJson.substring(1, rowJson.length - 1).replace("\\", "")
//            var settleJson = RoutersUtils.getStringFromBase64(intent.getStringExtra("settleJson"))
//            settleJson = settleJson.substring(1, settleJson.length-1).replace("\\", "")
//            if (!TextUtils.isEmpty(rowJson) && !TextUtils.isEmpty(settleJson)) {
//                val cartDataBean = Gson().fromJson(settleJson, CartDataBean::class.java)
//                val rowsBean = Gson().fromJson(rowJson, RowsBean::class.java)
//                mViewModel?.apply {
//                    shopCode = rowsBean.shopCode
//                    orgId = rowsBean.orgId
//                    mainGoodsSkuId = "${rowsBean.id}"
//                    isThirdCompany = rowsBean.isThirdCompany
//                    mainGoodsCount = ""
//                    mainGoodsPId = rowsBean.pId
//                    registerJumpType(SPELL_GROUP_RECOMMEND_SELECT_GOODS)
//
//                    val mainCartGoodsInfo = SpellGroupGoodsItem()
//                    mainCartGoodsInfo.goodsSelectedCount = cartDataBean!!.qty
//                    mainGoodsCount = cartDataBean.qty.toString() + ""
//                    mainCartGoodsInfo.skuId = rowsBean.id.toString() + ""
//                    mainCartGoodsInfo.goodsUrl = rowsBean.imageUrl
//                    mainCartGoodsInfo.goodsTitle = rowsBean.showName
//                    mainCartGoodsInfo.goodsUnit = rowsBean.productUnit
//                    mainCartGoodsInfo.goodsPrice = cartDataBean.price
//                    mainCartGoodsInfo.totalPrice = cartDataBean.totalAmount
//                    val spellGroupRecommendGoodsBean = SpellGroupRecommendGoodsBean(
//                        ArrayList(),
//                        mainCartGoodsInfo,
//                        HashMap(),
//                        CartGoodsInfo(),
//                        false
//                    )
//                    updateData(spellGroupRecommendGoodsBean, false)
//                }
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }

        mIsWholeSale = intent.getStringExtra("isPgby")
        XyyIoUtil.track("action_freeBuy", hashMapOf("shop_code" to (mViewModel?.shopCode
                ?: ""), "org_id" to (mViewModel?.orgId
                ?: ""), "sku_id" to (mViewModel?.mainGoodsSkuId ?: "")))


        /**
         *  这里和IOS保持一致 先不拼 随心拼标题
         * //        val mJgEntrance = intent.getStringExtra(IntentCanst.JG_ENTRANCE)?.let {
         * //            splicingPageTitle2Entrance(it, JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE)
         * //        } ?: JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE
         */
        val mJgEntrance = intent.getStringExtra(IntentCanst.JG_ENTRANCE) ?: ""

        val mJgActivityEntrance = intent.getStringExtra(IntentCanst.ACTIVITY_ENTRANCE)
        jgTrackBean = JgTrackBean(
                jgReferrer = this.getFullClassName(),
                jgReferrerTitle = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE,
                jgReferrerModule = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE,
                module = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE,
                pageId = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.PAGE_ID,
                title = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE,
                entrance = mJgEntrance,
                activityEntrance = mJgActivityEntrance)

        initTitleBar()

        initHeaderInfo()

        initContentList()


        iv_back.post {
            initCarts()
        }
    }


    private fun getShopCode(): String? {
        return mViewModel?.shopCode
    }

    private fun getOrgId(): String? {
        return mViewModel?.orgId
    }

    private fun isPopShop(): Boolean {
        return (mViewModel?.isThirdCompany ?: 0) == 1
    }

    /**
     * 初始化标题栏
     */
    private fun initTitleBar() {
        iv_back.setOnClickListener(this)
        iv_scan_qrcode.setOnClickListener(this)
        et_search.setOnClickListener(this)
        iv_clear.setOnClickListener(this)
        iv_service.setOnClickListener(this)
        tv_settle.setOnClickListener(this)
    }

    /**
     * 初始化店铺信息
     */
    private fun initHeaderInfo() {
        // 店铺请求回调
        mShopInfoModel.let {
            it.shopInfoPopLiveData.observe(this) { bean ->
                popShopInfo = bean
                updateHeaderInfoByPop()
            }
            it.shopInfoSelfLiveData.observe(this) { bean ->
                selfShopInfo = bean
                updateHeaderInfoBySelf()
            }
            it.onLineMessageLiveData.observe(this) { bean ->
                this.popImUrl = bean?.IM_PACK_URL

            }
        }
        // 请求店铺数据
        if (isPopShop()) {
            // pop
            mShopInfoModel.getShopInfoPop(SpUtil.getMerchantid(), getOrgId() ?: "", true)
        } else {
            // 自营
            mShopInfoModel.getSelfShopInfo(SpUtil.getMerchantid(), getShopCode() ?: "")
        }

        //获取tranNo后跳转提单页
        spellGroupRecommendSelectViewModel.spellGroupTranNoLiveData.observe(this, Observer {
            if (it.isSuccess) {
//                val jump = {
//                    mViewModel?.clearRecommendGoods()
//                    RoutersUtils.open(
//                        mViewModel?.getJumpRouter(
//                            CURRENT_PAGE_SELECT_RECOMMEND_GOODS, hashMapOf(
//                                "tranNo" to it.data.tranNo,
//                                "skuId" to mViewModel!!.mainGoodsSkuId,
//                                "productNum" to mViewModel!!.mainGoodsCount
//                            )
//                        )
//                    )
//                }
//                if (it.data?.isShowDialog == 1) {
//                    AlertDialogEx(this@SpellGroupRecommendSelectedGoodsActivity)
//                        .setMessage("您的资质已过期，请及时更新，以免影响发货")
//                        .setCanceledOnTouchOutside(false)
//                        .setConfirmButton("我知道了") {_, _-> jump()}
//                        .show()
//                } else jump()

                RoutersUtils.open(
                    mViewModel?.getJumpRouter(
                        CURRENT_PAGE_SELECT_RECOMMEND_GOODS, hashMapOf(
                            "tranNo" to it.data.tranNo,
                            "skuId" to mViewModel!!.mainGoodsSkuId,
                            "productNum" to mViewModel!!.mainGoodsCount,
                            "isPgby" to (mIsWholeSale ?: ""),
                            "isSupportOldSxp" to "1",
                            IntentCanst.JG_ENTRANCE to (jgTrackBean?.entrance ?: ""),
                            IntentCanst.ACTIVITY_ENTRANCE to (jgTrackBean?.activityEntrance ?: ""),
                    )
                    )
                )
            }
        })
    }


    private fun updateHeaderInfoBySelf() {
        loadRoundCornerImage(
            this,
            selfShopInfo?.shopLogoUrl,
            iv_shop_logo,
            4
        )
        tv_shop_name.text = selfShopInfo?.shopName ?: "--"
        tv_tag_shipping_location.visibility = View.GONE
        handleTags(selfShopInfo?.shopPropTags, selfShopInfo?.shopServiceQualityTags)
    }

    private fun handleTags(shopPropTags: List<TagBean>?, shopServiceQualityTags: List<TagBean>?) {
        var isShopTags1 = false
        var isShopTags2 = false
        shopPropTags?.apply {
            if (isNotEmpty()) {
                isShopTags1 = true
                sntv_tag_list.bindData(shopPropTags, "", Integer.MAX_VALUE)
                sntv_tag_list.visibility = View.VISIBLE
            }
        }
        shopServiceQualityTags?.apply {
            if (isNotEmpty()) {
                isShopTags2 = true
                tv_shop_desc.visibility = View.VISIBLE
                val tagStrBuilder = SpannableStringBuilder("")
                forEachIndexed { index, tagBean ->
                    tagStrBuilder.append(tagBean.text)
                    if (index != size - 1) {
                        val symbolBuilder = SpannableStringBuilder("|");
                        symbolBuilder.setSpan(
                            ForegroundColorSpan(Color.parseColor("#EEEEEE")),
                            0,
                            1,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        tagStrBuilder.append(" ")
                            .append(symbolBuilder)
                            .append(" ")
                    }
                }
                tv_shop_desc.text = tagStrBuilder

            }
        }
//        space_shop_tag.visibility = if (isShopTags1 || isShopTags2) View.VISIBLE
//        else View.GONE
    }

    private fun updateHeaderInfoByPop() {
        loadRoundCornerImage(this, popShopInfo?.orgLogo, iv_shop_logo, 4)
        tv_shop_name.text = popShopInfo?.orgName ?: "--"
        if (popShopInfo?.deliveryAddress.isNullOrEmpty()) {
            tv_tag_shipping_location.visibility = View.GONE
        } else {
            tv_tag_shipping_location.visibility = View.VISIBLE
            tv_tag_shipping_location.text = "${popShopInfo?.deliveryAddress}发货"
        }
        handleTags(popShopInfo?.shopPropTags, popShopInfo?.shopServiceQualityTags)
    }


    override fun onResume() {
        super.onResume()
        updateCartsDetail()
        goodlistAdapter?.notifyDataSetChanged()
    }

    /**
     * 初始化商品列表
     */
    private fun initContentList() {
        rg_property?.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.rb_01 -> {
                    property = "smsr.sale_num"
                    jgTrackSpellGroupRecommendSelectedGoodsBtnClick("精选")
                }    // 精选
                R.id.rb_02 -> {
                    property = "spa.sale_num"
                    jgTrackSpellGroupRecommendSelectedGoodsBtnClick("热门")
                }     // 热门
            }
            getNewGoodlist()
        }
        categoryAdapter = SpellGroupCommendSelectedGoodsCategoryAdapter(
            R.layout.item_spell_group_recommend_selected_goods_category,
            floorData
        ).also { adapter ->
            rv_floor_index?.let {
                it.layoutManager = LinearLayoutManager(this)
                it.adapter = adapter
            }
            adapter.setOnItemClickListener { _, view, position ->
                // 刷新品类信息
                if (!floorData[position].isSelect) {
                    floorData.forEach { it.isSelect = false }

                    floorData[position].isSelect = true
                    floorId = floorData[position].floorId
                    //floorType = floorData.get(position).floorType

                    adapter.notifyDataSetChanged()
                    // 获取对应楼层的商品信息
                    getNewGoodlist()
                }
            }
        }
        val mainGoods = mViewModel?.spellGroupRecommendGoodsLiveData?.value?.mainRowsBean
        goodlistAdapter = SpellGroupRecommendSelectedGoodsAdapter(
            goodsData,
            mViewModel?.spellGroupRecommendGoodsLiveData,
            mainGoods,
            object : AddCartCallback {
                override fun addCart(
                    goodsItem: SpellGroupGoodsItem,
                    isAdd: Boolean,
                    goodsAmount: Int
                ) {
                    mViewModel?.addSpellGroupRecommendCart(
                        goodsItem, isAdd, goodsAmount
                    )
                }

                override fun checkCollect(
                    rowsBean: RowsBean,
                    potion: Int,
                    adapter: RecyclerView.Adapter<*>
                ) {
                    <EMAIL>(
                        rowsBean,
                        potion,
                        adapter
                    )
                }

            }).apply {
            setEmptyView(
                this@SpellGroupRecommendSelectedGoodsActivity,
                R.layout.layout_empty_view_all_goods,
                R.drawable.icon_empty,
                "哇哦，没有找到相关商品"
            )

            jgTrackBean = <EMAIL>
        }
        goodlistAdapter?.setOnLoadMoreListener({ getLoadMoreGoodlist() }, rv_goodslist)
        smartrefresh?.setOnRefreshListener {
            getNewGoodlist()
        }
        rv_goodslist.itemAnimator = null
        rv_goodslist.layoutManager = LinearLayoutManager(this)
        rv_goodslist.adapter = goodlistAdapter
        getGoodsIndex()

    }

    private fun getGoodsIndex() {
        HttpManager.getInstance()
            .post(AppNetConfig.LIST_GROUP_BUYING_PRODUCT_CATEGORIES, RequestParams(), object : BaseResponse<SuiXinPinProductCategoriesData>() {
                override fun onSuccess(content: String?, obj: BaseBean<SuiXinPinProductCategoriesData>?, t: SuiXinPinProductCategoriesData?) {
                    super.onSuccess(content, obj, t)
                    if (obj?.isSuccess == true) {
                        t?.rows?.let {
                            floorData.clear()
                            it.forEach { category->
                                floorData.add(ShopHomeIndexBean.Floor().apply {
                                    floorId = category.id
                                    floorName = category.name
                                })
                            }
                            if (floorData.size > 0) {
                                floorData.get(0).isSelect = true
                                floorId = floorData.get(0).floorId
                                //floorType = floorData.get(0).floorType
                                getNewGoodlist()
                            }
                            categoryAdapter.notifyDataSetChanged()
                        }
                    }
                }
            })
    }


    /**
     * 请求数据
     */
    private fun getLoadMoreGoodlist() {
        HttpManager.getInstance().post(AppNetConfig.SORTNET,
            getNewGoodlistRequestParams(true), object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<SearchResultBean?>?,
                    brandBean: SearchResultBean?
                ) {
                    brandBean?.let { updateGoodsData(false, it) }
                }
            })
    }

    private fun getNewGoodlist() {
        showProgress()
        val newgoodparams = getNewGoodlistRequestParams(false)
        HttpManager.getInstance()
            .post(AppNetConfig.SORTNET, newgoodparams, object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<SearchResultBean?>,
                    brandBean: SearchResultBean?
                ) {
                    val flowData = BaseFlowData(brandBean?.sptype, brandBean?.spid, brandBean?.sid)
                    goodlistAdapter?.setFlowData(flowData)
                    dismissProgress()
                    smartrefresh?.finishRefresh()
                    goodsData.clear()
                    brandBean?.let {
                        updateGoodsData(true, it)
                    }
                }

                override fun onFailure(error: NetError) {
                    dismissProgress()
                    smartrefresh.finishRefresh()
                }
            })
    }


    /**
     * 收藏-取消收藏
     */
    private fun checkCollect(rowsBean: RowsBean, potion: Int, adapter: RecyclerView.Adapter<*>) {
        val id = rowsBean.id
        val collectNet =
            if (rowsBean.favoriteStatus == 1) AppNetConfig.CANCEL_COLLECT else AppNetConfig.COLLECT
        val collectStr = if (rowsBean.favoriteStatus == 1) "取消收藏" else "收藏成功"
        val params = RequestParams()
        val merchantId = SpUtil.getMerchantid()
        params.put("merchantId", merchantId)
        params.put("skuId", id.toString())
        HttpManager.getInstance().post(collectNet, params, object : BaseResponse<EmptyBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<EmptyBean?>?, t: EmptyBean?) {

                if (obj != null && obj.isSuccess) {
                    if (rowsBean.favoriteStatus == 1) {
                        rowsBean.setFavoriteStatus(2)
                    } else {
                        rowsBean.setFavoriteStatus(1)
                        AlertDialogEx(this@SpellGroupRecommendSelectedGoodsActivity)
                            .setMessage("订阅成功")
                            .setConfirmButton("我知道了", object : AlertDialogEx.OnClickListener {
                                override fun onClick(dialog: AlertDialogEx?, button: Int) {
                                    dialog?.dismiss()
                                }

                            }).show()
                    }
                    ToastUtils.showLong(collectStr)
                    adapter.notifyItemChanged(potion)
                }

            }
        })
    }


    /**
     *  更新商品信息
     */
    private fun updateGoodsData(isRefresh: Boolean, rowsBeans: SearchResultBean) {
        searchMoreParams = rowsBeans.requestParams
        rowsBeans.rows?.let {
            AdapterUtils.addLocalTimeForRows(rowsBeans.rows)
            goodlistAdapter?.let {
                AdapterUtils.notifyAndControlLoadmoreStatus(
                    rowsBeans.rows,
                    it,
                    isRefresh,
                    rowsBeans.isEnd
                )
            }
            // 请求并更新折后价
            goodlistAdapter?.let { AdapterUtils.getAfterDiscountPrice(rowsBeans.rows, it) }
        }
    }


    /**
     * 请求参数
     */
    private fun getNewGoodlistRequestParams(loadMore: Boolean): RequestParams? =
        if (loadMore) searchMoreParams else RequestParams().apply {
            put("merchantId", SpUtil.getMerchantid())
            getShopCode()?.let {
                put("shopCodes", it)
            }
            put("displayCategoryId", "${floorId ?: -1}")
            put("property", property)
            put("direction", "desc")
            put("tagList", "YBM_ACT_SUI_XIN_PIN")
            put("isThirdCompany", "${if (isPopShop()) 1 else 0}")
            mViewModel?.mainGoodsPId?.let { put("excludeIds", it) }
            if (!paramsMap.containsKey("sptype")) {
                put("sptype", "3")
            }
            mViewModel?.mainGoodsPId?.let { put("excludeIds", it) }
        }

    /**
     * 初始化购物车
     */
    private fun initCarts() {
        v_cart_bg.setOnClickListener(this)
        tv_settle.setOnClickListener(this)
        v_mask.setOnClickListener(this)
        v_cart_bg.post {
            if (ll_carts_detail.height != 0) {
                ll_carts_detail.visibility = View.VISIBLE
                ll_carts_detail.translationY = ll_carts_detail.height.toFloat()
            }
        }
        mViewModel?.spellGroupRecommendGoodsLiveData?.observe(this) {
            updateCartsInfo(it)
            updateCartsDetail()
        }
        updateCartsInfo(mViewModel?.spellGroupRecommendGoodsLiveData?.value)
        updateCartsDetail()

    }

    private fun updateCartsDetail() {
        val goodsBean = mViewModel?.spellGroupRecommendGoodsLiveData?.value
        // 主商品
        ImageUtil.load(
            this,
            AppNetConfig.LORD_IMAGE + goodsBean?.mainRowsBean?.goodsUrl,
            getView<ImageView>(R.id.iv_main_logo)
        )
        getView<TextView>(R.id.tv_main_name).text =
            goodsBean?.mainRowsBean?.goodsTitle ?: "--"
        getView<TextView>(R.id.tv_main_count).text =
            "x${goodsBean?.mainRowsBean?.goodsSelectedCount ?: 0}"
        getView<TextView>(R.id.tv_main_price).text =
            "¥${goodsBean?.mainRowsBean?.goodsPrice ?: "--"}/${goodsBean?.mainRowsBean?.goodsUnit ?: "--"}".let { priceText ->
                getFormatPriceText(priceText)
            }
        // 随心拼商品
        getView<RecyclerView>(R.id.rv_sub_list).let { subList ->
            val tempReversedList = goodsBean?.rowsBean?.filter {
                goodsBean.goodsIdMapping[it.skuId+""] !=0
            }
            if (tempReversedList == null || tempReversedList.isEmpty()) {
                subList.visibility = View.GONE
                getView<View>(R.id.group_empty).visibility = View.VISIBLE
            } else {
                subList.visibility = View.VISIBLE
                getView<View>(R.id.group_empty).visibility = View.GONE
                if (subList.adapter == null) {
                    subList.layoutManager = LinearLayoutManager(this)
                    subList.adapter =
                        SpellGroupRecommendSelectedGoodsCartsAdapter(mViewModel?.spellGroupRecommendGoodsLiveData,
                            object : AddCartCallback {
                                override fun addCart(
                                    goodsItem: SpellGroupGoodsItem,
                                    isAdd: Boolean, goodsAmount: Int
                                ) {
                                    hideSoftInput()
                                    mViewModel?.addSpellGroupRecommendCart(
                                        goodsItem, isAdd, goodsAmount
                                    )
                                }

                                override fun checkCollect(
                                    rowsBean: RowsBean,
                                    potion: Int,
                                    adapter: RecyclerView.Adapter<*>
                                ) {
                                    // do nothing
                                }
                            }).also { adapter ->
                            cartsAdapter = adapter
                        }
                } else {
                    cartsAdapter?.setNewData(tempReversedList)
                    cartsAdapter?.notifyDataSetChanged()
                }
            }
        }
    }

    private fun getFormatPriceText(priceText: String): SpannableString {
        val sb = SpannableString(priceText)
        sb.setSpan(
            AbsoluteSizeSpan(ConvertUtils.dp2px(12f)),
            0,
            1,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        sb.setSpan(
            StyleSpan(Typeface.BOLD),
            0,
            1,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )

        val pointIndex = priceText.indexOf(".");
        val priceEndIndex = priceText.indexOf("/");
        if (pointIndex != -1) {
            sb.setSpan(
                AbsoluteSizeSpan(ConvertUtils.dp2px(20f)),
                1,
                pointIndex,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
            sb.setSpan(
                AbsoluteSizeSpan(ConvertUtils.dp2px(14f)),
                pointIndex,
                priceEndIndex,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        sb.setSpan(
            StyleSpan(Typeface.BOLD),
            1,
            priceEndIndex,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        sb.setSpan(
            StyleSpan(Typeface.NORMAL),
            priceEndIndex,
            sb.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        sb.setSpan(
            ForegroundColorSpan(Color.parseColor("#575766")),
            priceEndIndex,
            sb.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        sb.setSpan(
            AbsoluteSizeSpan(ConvertUtils.dp2px(12f)),
            priceEndIndex,
            sb.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        return sb
    }


    private fun updateCartsInfo(goodsBean: SpellGroupRecommendGoodsBean?) {
        tv_cart_count.text =
            "${goodsBean?.getCartInfoWithMainRowsBean()?.goodsCategoriesCount ?: 0}"
        tv_cart_price.text =
            UiUtils.transform(goodsBean?.getCartInfoWithMainRowsBean()?.totalPrice ?: "0.00").let {
                var priceText = SpannableString("¥${it}")
                priceText.setSpan(
                    AbsoluteSizeSpan(ConvertUtils.dp2px(10f)),
                    0,
                    1,
                    Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                )
                priceText
            }
        tv_cart_count_text.text =
            "共${goodsBean?.getCartInfoWithMainRowsBean()?.goodsTotalCount ?: 0}件商品"

    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_back -> onBackPressed()
            R.id.iv_scan_qrcode -> jumpScanQRCodePage()
            R.id.et_search -> jumpSearchPage()
            R.id.iv_clear -> clickClearOrVoice()
            R.id.iv_service -> jumpCustomerServicePage()
            R.id.v_cart_bg -> showCartsDialog()
            R.id.tv_settle -> jumpSettlePage()
            R.id.v_mask -> showCartsDialog()
        }
    }

    override fun onBackPressed() {
        if (ll_carts_detail.translationY <= 0 && ll_carts_detail.visibility == View.VISIBLE) {
            //已展开,需要折叠
            showCartsDialog()
        } else {
            RoutersUtils.open(
                mViewModel!!.getFinishRouter(
                    CURRENT_PAGE_SELECT_RECOMMEND_GOODS,
                    HashMap()
                )
            )
        }
    }

    /**
     * 跳转提单页
     */
    private fun jumpSettlePage() {
        XyyIoUtil.track(
            "action_freeBuy_checkOut", hashMapOf(
                "shop_code" to (mViewModel?.shopCode ?: ""),
                "org_id" to (mViewModel?.orgId ?: ""),
                "sku_id" to (mViewModel?.mainGoodsSkuId ?: "")
            )
        )
        if (!mViewModel?.mainGoodsSkuId.isNullOrEmpty() || !mViewModel?.mainGoodsCount.isNullOrEmpty()) {
            // 在发送请求前先清理数量为0的商品，确保不会将无效商品带到接口中
            mViewModel?.clearRecommendGoods()

            spellGroupRecommendSelectViewModel.getSpellGroupTranNo(
                mViewModel!!.mainGoodsSkuId,
                mViewModel!!.mainGoodsCount
            )
        }
    }

    var isInAnimation = false

    /**
     * 打开购物车
     */
    private fun showCartsDialog() {
        XyyIoUtil.track(
            "action_freeBuy_cmmodity", hashMapOf(
                "shop_code" to (mViewModel?.shopCode ?: ""),
                "org_id" to (mViewModel?.orgId ?: ""),
                "sku_id" to (mViewModel?.mainGoodsSkuId ?: "")
            )
        )
        if (isInAnimation) {
            return
        }
        if (ll_carts_detail.translationY <= 0 && ll_carts_detail.visibility == View.VISIBLE) {
            // 已展开，需要折叠
            val animator = ObjectAnimator.ofFloat(
                ll_carts_detail,
                "translationY",
                0f,
                ll_carts_detail.height.toFloat()
            )
                .setDuration(200)
            animator.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    isInAnimation = true
                }

                override fun onAnimationEnd(animation: Animator) {
                    isInAnimation = false
                    goodlistAdapter?.notifyDataSetChanged()
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }

            })
            animator.start()

        } else {
            updateCartsDetail()
            ll_carts_detail.visibility = View.VISIBLE
            // 已折叠，需要展开
            val animator = ObjectAnimator.ofFloat(
                ll_carts_detail,
                "translationY",
                ll_carts_detail.height.toFloat(),
                0f
            ).setDuration(200)
            animator.addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    isInAnimation = true
                }

                override fun onAnimationEnd(animation: Animator) {
                    isInAnimation = false
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }

            })
            animator.start()
        }
    }

    private fun getShopName(): String {
        if (isPopShop()) {
            return popShopInfo?.orgName ?: ""
        } else {
            return selfShopInfo?.shopName ?: ""
        }
    }

    /**
     * 跳转扫码
     */
    private fun jumpScanQRCodePage() {
        jgTrackSpellGroupRecommendSelectedGoodsBtnClick("搜索框-扫一扫")
        val openNextPageUrl =
            RoutersUtils.urlEncode("ybmpage://searchspellgrouprecommendgoods?shopName=${getShopName()}&keyword=")
        RoutersUtils.open("ybmpage://captureactivity?current_page=spellgrouprecommendselectedgoodsactivity&$OPENNEXTPAGEURLKEY=${openNextPageUrl}")
    }

    /**
     * 跳转搜索
     */
    private fun jumpSearchPage() {
        jgTrackSpellGroupRecommendSelectedGoodsBtnClick("搜索框")
        RoutersUtils.open("ybmpage://searchspellgrouprecommendgoods?shopName=${getShopName()}")
    }

    /**
     * 点击语音
     */
    private fun clickClearOrVoice() {
        jgTrackSpellGroupRecommendSelectedGoodsBtnClick("搜索框-语音")
        val openNextPageUrl =
            RoutersUtils.urlEncode("ybmpage://searchspellgrouprecommendgoods?shopName=${getShopName()}&keyword=")
        RoutersUtils.open("ybmpage://searchvoiceactivity?fromPage=spellgrouprecommendselectedgoodsactivity&$OPENNEXTPAGEURLKEY=${openNextPageUrl}")
    }

    /**
     * 跳转客服
     */
    private fun jumpCustomerServicePage() {
        jgTrackSpellGroupRecommendSelectedGoodsBtnClick("联系客服")
        if (isPopShop()) {
            popImUrl?.let {
                RoutersUtils.open(
                    RoutersUtils.getRouterPopCustomerServiceUrl(
                        it,
                        getOrgId(),
                        "",
                        popShopInfo?.orgName
                    )
                )
            }
        } else {
            selfShopInfo?.iMPackUrl?.let {
                RoutersUtils.open(it)
            }
        }
    }

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: MutableMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE
        return properties
    }

    override fun registerPageUrl(): String  = this.getFullClassName()

}