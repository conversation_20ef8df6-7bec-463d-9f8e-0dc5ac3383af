package com.ybmmarket20.viewmodel

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponsTipsResponse
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.OrderStatusNumber
import com.ybmmarket20.bean.PurchaseSwitchBean
import com.ybmmarket20.bean.ShoppingGoldRechargeBean
import com.ybmmarket20.network.request.Mine2CommonToolsRequest
import com.ybmmarket20.network.request.SearchDataRequest
import com.ybmmarket20.network.request.SwitchRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 开关
 */
class SwitchViewModel(app: Application): BaseViewModel(app) {

    private val _purchaseSwitchStatusLiveData = MutableLiveData<BaseBean<PurchaseSwitchBean>>()
    val purchaseSwitchStatusLiveData: LiveData<BaseBean<PurchaseSwitchBean>> = _purchaseSwitchStatusLiveData

    //订单气泡数量
    private val _ordersBubbleCountData: MutableLiveData<BaseBean<OrderStatusNumber>> = MutableLiveData()
    val ordersBubbleCountData: LiveData<BaseBean<OrderStatusNumber>> = _ordersBubbleCountData

    //购物金充值
    private val _shoppingGoldRechargeBeanData: MutableLiveData<BaseBean<ShoppingGoldRechargeBean>> = MutableLiveData()
    val shoppingGoldRechargeBeanData: LiveData<BaseBean<ShoppingGoldRechargeBean>> = _shoppingGoldRechargeBeanData

    //顶部优惠券
    private val _couponBeanData: MutableLiveData<BaseBean<CouponsTipsResponse>> = MutableLiveData()
    val couponBeanData: LiveData<BaseBean<CouponsTipsResponse>> = _couponBeanData

    //领券接口
    private val _voucherLiveData = MutableLiveData<Pair<String,BaseBean<EmptyBean>>>()
    val voucherLiveData: LiveData<Pair<String,BaseBean<EmptyBean>>> = _voucherLiveData


    fun getVoucher(voucherTemplateId: String?,mUrl:String) {
        viewModelScope.launch {
            if (!TextUtils.isEmpty(voucherTemplateId)) {
                val paramsMap = mutableMapOf<String, String>()
                paramsMap["merchantId"] = SpUtil.getMerchantid()
                paramsMap["voucherTemplateId"] = voucherTemplateId?:""
                val voucher = SearchDataRequest().getVoucher(paramsMap)?:return@launch
                _voucherLiveData.postValue(Pair(mUrl,voucher))
            }
        }
    }

    fun getPurchaseSwitchStatus() {
        viewModelScope.launch {
            val result = SwitchRequest().purchaseOrderSwitch(SpUtil.getMerchantid())
            _purchaseSwitchStatusLiveData.postValue(result)
        }
    }

    fun getSceneData() {
        viewModelScope.launch {
            if (!SpUtil.getMerchantid().isNullOrEmpty()) {
                val result = SwitchRequest().getSceneData(SpUtil.getMerchantid())
                _couponBeanData.postValue(result)
            }
        }
    }

    /**
     * 获取订单气泡数量
     */
    fun getOrdersBubbleCount() {
        viewModelScope.launch {
            val ordersBubbleCountBean = Mine2CommonToolsRequest().getOrdersBubbleCount(SpUtil.getMerchantid())
            _ordersBubbleCountData.postValue(ordersBubbleCountBean)
        }
    }

    fun getShoppingGoldRechargeBean() {
        viewModelScope.launch {
            val shoppingGoldRechargeBean = Mine2CommonToolsRequest().getShoppingGoldRechargeBean(SpUtil.getMerchantid())
            _shoppingGoldRechargeBeanData.postValue(shoppingGoldRechargeBean)
        }
    }
}